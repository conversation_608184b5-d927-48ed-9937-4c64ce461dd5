{
  "$schema": "https://json.schemastore.org/eslintrc.json",
  "extends": [
    "next/core-web-vitals",
    "airbnb",
    "airbnb-typescript",
    "plugin:prettier/recommended",
    "plugin:tailwindcss/recommended"
  ],
  "parserOptions": {
    "project": "./tsconfig.json",
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": "latest",
    "sourceType": "module"
  },
  "overrides": [
    {
      "files": [ "*.ts", "*.tsx" ],
      "parser": "@typescript-eslint/parser"
    },
    {
      "files": [ "next.config.js", "next.config.mjs" ],
      "parser": "@typescript-eslint/parser",
      "parserOptions": {
        "project": null
      },
      "rules": {
        "@typescript-eslint/no-var-requires": "off",
        "import/no-extraneous-dependencies": "off",
        "@typescript-eslint/explicit-function-return-type": "off"
      }
    }
  ],
  "plugins": [ "simple-import-sort", "prettier", "@typescript-eslint" ],
  "rules": {
    // "sort-imports": "off",
    // "import/order": "off",
    "import/order": [
      "error",
      {
        "groups": [
          "builtin", // Node.js wbudowane (np. fs, path)
          "external", // Pakiety npm
          "internal", // Wewnętrzne aliasy (np. @app, @components)
          [ "parent", "sibling" ], // Relatywne importy
          "index", // Importy z tego samego folderu
          "object", // Importy obiektów
          "type" // Importy typów
        ],
        "pathGroups": [
          {
            "pattern": "react",
            "group": "builtin",
            "position": "before"
          },
          {
            "pattern": "next/**",
            "group": "builtin",
            "position": "before"
          },
          {
            "pattern": "@app/**",
            "group": "internal",
            "position": "before"
          },
          {
            "pattern": "@components/**",
            "group": "internal",
            "position": "before"
          },
          {
            "pattern": "*.css",
            "group": "index",
            "position": "after"
          },
          {
            "pattern": "*.scss",
            "group": "index",
            "position": "after"
          }
        ],
        "pathGroupsExcludedImportTypes": [ "react", "next" ],
        "newlines-between": "always",
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],

    "simple-import-sort/imports": "off",

    "import/first": "error",
    "import/newline-after-import": "error",
    "import/no-duplicates": "error",

    "tailwindcss/no-custom-classname": 0,
    "react/react-in-jsx-scope": [ "off" ],
    "react/jsx-uses-react": [ "off" ],
    "import/no-extraneous-dependencies": "off",
    "@next/next/no-img-element": 0,
    "default-case": 0,
    "no-await-in-loop": 0,
    "no-undef": 0,
    "no-return-assign": [ "error", "except-parens" ],
    "no-restricted-syntax": [
      2,
      "ForInStatement",
      "LabeledStatement",
      "WithStatement"
    ],
    "no-unused-vars": [
      1,
      {
        "ignoreRestSiblings": true,
        "argsIgnorePattern": "toast|props|res|next|^err"
      }
    ],
    "prefer-const": [
      "error",
      {
        "destructuring": "all"
      }
    ],
    "no-unused-expressions": [
      1,
      {
        "allowTaggedTemplates": true
      }
    ],
    "no-param-reassign": [
      2,
      {
        "props": false
      }
    ],
    "import/prefer-default-export": 0,
    "import": 0,
    "func-names": 0,
    "space-before-function-paren": 0,
    "comma-dangle": 0,
    "max-len": 0,
    "object-shorthand": 0,
    "import/extensions": 0,
    "no-underscore-dangle": 0,
    "consistent-return": 0,
    "react/display-name": 1,
    "react/no-array-index-key": 0,
    "react/prefer-stateless-function": 0,
    "react/forbid-prop-types": 0,
    "react/no-unescaped-entities": 0,
    "react/no-unknown-property": 0,
    "react/jsx-props-no-spreading": 0,
    "react/jsx-no-constructed-context-values": 0,
    "react/function-component-definition": 0,
    "react/prop-types": 0,
    "react/no-unstable-nested-components": 0,
    "jsx-a11y/accessible-emoji": 0,
    "jsx-a11y/anchor-has-content": 0,
    "jsx-a11y/heading-has-content": 0,
    "jsx-a11y/label-has-associated-control": [
      "error",
      {
        "assert": "either"
      }
    ],
    "react/require-default-props": 0,
    "react/jsx-filename-extension": [
      1,
      {
        "extensions": [ ".js", ".jsx", ".ts", ".tsx", ".mdx" ]
      }
    ],
    "radix": 0,
    "@typescript-eslint/no-shadow": [
      "error",
      {
        "allow": [
          "resolve",
          "reject",
          "done",
          "next",
          "err",
          "error",
          "props",
          "toast",
          "api",
          "config"
        ]
      }
    ],
    "@typescript-eslint/no-unused-vars": [
      "warn",
      {
        "argsIgnorePattern": "^_",
        "varsIgnorePattern": "^_",
        "caughtErrorsIgnorePattern": "^_",
        "ignoreRestSiblings": true
      }
    ],
    "prettier/prettier": [
      "error",
      {
        "trailingComma": "es5",
        "endOfLine": "auto"
      }
    ],
    "jsx-a11y/href-no-hash": "off",
    "jsx-a11y/anchor-is-valid": [
      "warn",
      {
        "aspects": [ "invalidHref" ]
      }
    ],
    "@typescript-eslint/lines-between-class-members": "off",
    "@typescript-eslint/no-throw-literal": "off",
    "@typescript-eslint/array-type": "off",
    "@typescript-eslint/consistent-type-definitions": "off",
    "@typescript-eslint/no-use-before-define": "off",
    "@typescript-eslint/no-empty-interface": [
      "error",
      { "allowSingleExtends": true }
    ],
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "@typescript-eslint/comma-dangle": [ "off" ]
  }
}