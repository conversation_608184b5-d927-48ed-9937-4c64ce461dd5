Dziennik pisania kodu 

# 1-create-salary-range-text-field

22.09.2024  **2h**


- Dodano kilka ikon do assets / icons 
- Dodano w src/app folder elements i w nim plik text-fields.tsx który ma podstawowe text fieldy dla aplikacji 
- Dodan<PERSON> stronę testową /marcel_test na której sobie testowałem elementy 

24.09.2024 **2h**

- Dodano w /elements plik radio.tsx w którym stworzyłem podstawowe radio buttons dla lokalizacji 
- Dodano kolejne dwa TextFieldy dla CV, Strona Internetowa (Z ikoną) 

04.10.2024 **2h**

- Zmieniono style dla text-fileds.tsx i radio.tsx, teraz używamy zmiennych oraz kolorów zdefiniowanych w globals.css
- Dodano zmienną maxWidth orad transitionDuration dla powyższych elementów 
- Dodano plik /documentation/documentation.md z podstawowymi zasadami tego projektu 
- W<PERSON><PERSON><PERSON><PERSON><PERSON>ono defaultowe pliki nextjs

# 2-create-custom-button

04.10.2024 **2h**

- Dodano plik buttons.tsx 
- Dodano dwa pierwsze typy przycisków 'primary' i 'secondary'

05.10.2024 **1h**

- Dodano dwa nowe rodzaje buttonow ButtonAsIcon oraz ButtonCross 

06.10.2024 **4h**

- Dodano całą logikę pod tworzenie 'galerii' wyświetlania dostępnych komponentów / elementów które stworzyliśmy 
- Tymczasowo będą pod url /d/<nazwa_elementu>, możemy łatwiej widzieć na żywo wszystkie dostępne elementy (np. buttons) i edytować ich wygląd 
- Wyczyściłem trochę kodu dla buttons, radio i text-fields 
- Dodałem plik /elements/_shared.ts w którym umiesciłem wspólne zmienne oraz typy


13.10.2024 **2h** 

- Przywrócono style dla shadcn 
- Wyczyszczono kod dla text-fileds, buttons, radio
- Dodano wstępny wygląd dla komponentu navbar 

15.10.2024 **3h** 

- Lekka poprawa elementów oraz ulepszenie ich o responsywność 
- Ukończono robienie Navbar component
- Dodano opcję dla Navbar aby go 'Otworzyć' po prawej stronie ekranu (Burger menu) 

23.10.2024 **2h**

- Dodano podstawową logikę dla modali 
- Dodano plik /store/ModalContext
- Dodano /components/modals ModalLayout i Modals.tsx 

27.10.2024 **2h**

- Dodano Modal login, signin, forgot password 
- Dodano logikę do wracania do poprzedniego modala
- Kilka drobnych bug fixów z modalami