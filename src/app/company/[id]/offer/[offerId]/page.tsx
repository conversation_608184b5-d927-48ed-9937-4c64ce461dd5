'use client';

import { useMemo } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useTranslations } from 'next-intl';

import { borderRadiusStyles } from '@app/elements/_shared';

import { ApplicationList } from '@components/Company/components/Applications';
import { CompanyTabs } from '@components/Company/components/CompanyTabs';
import {
  JobOffer,
  mapJobOfferToOfferDetailedProps,
} from '@components/Company/types';
import Loader from '@components/Loader/Loader';
import OfferOverview from '@components/Offers/OfferOverview/OfferOverview';

import { useApplications } from '@actions/hooks/useApplications';
import { useCompany } from '@actions/hooks/useCompany';

export default function CompanyOffer({
  params: { id, offerId },
}: {
  params: {
    id: number;
    offerId: number;
  };
}) {
  const { data, isLoading } = useCompany(id);
  const { data: applicationsData, isLoading: isLoadingApplications } =
    useApplications(Number(offerId));
  const searchParams = useSearchParams();

  const router = useRouter();
  const t = useTranslations('Company');

  const activeDetailTab = (searchParams.get('detailTab') || 'offer') as
    | 'offer'
    | 'applications';

  const companyData = data?.data;

  const selectedOffer = useMemo(
    // eslint-disable-next-line eqeqeq
    () => companyData?.jobOffers.find((offer) => offer.id == offerId),
    [companyData, offerId]
  );

  const isSelectedOfferActive = useMemo(() => {
    if (!selectedOffer) return false;

    const monthAgo = new Date();
    monthAgo.setMonth(monthAgo.getMonth() - 1);

    return new Date(selectedOffer.createdDate) > monthAgo;
  }, [selectedOffer]);

  const handleDetailTabChange = (tab: 'offer' | 'applications') => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('detailTab', tab);
    router.replace(`?${newParams.toString()}`);
  };

  const prepareOfferForMapping = (offer: JobOffer): JobOffer => {
    const offerCopy = { ...offer } as any;

    if (
      companyData &&
      (!offerCopy.aboutCompany ||
        offerCopy.aboutCompany === 'Informacje o firmie nie są dostępne')
    ) {
      //   !modifiedOfferFields[offer.id]?.includes('aboutCompany')
      offerCopy.aboutCompany = companyData.aboutCompany;
    }

    return offerCopy as JobOffer;
  };

  const preparedOffer = selectedOffer && prepareOfferForMapping(selectedOffer);
  const mappedOffer =
    preparedOffer && mapJobOfferToOfferDetailedProps(preparedOffer);

  if (isLoading || isLoadingApplications) {
    return <Loader />;
  }

  if (!data?.data) {
    return (
      <div className="flex justify-center p-10">{t('companyNotFound')}</div>
    );
  }

  return (
    <div
      className={`mx-auto flex size-full max-w-4xl flex-col gap-4 ${borderRadiusStyles.extra}`}
    >
      <CompanyTabs
        onBackClick={() => router.back()}
        selectedOffer={selectedOffer}
        applicationCount={applicationsData?.data?.length}
        activeDetailTab={activeDetailTab}
        onDetailTabChange={handleDetailTabChange}
      />

      {activeDetailTab === 'offer' && mappedOffer && (
        <OfferOverview
          offer={mappedOffer}
          showButton={false}
          isEditable={isSelectedOfferActive}
        />
      )}

      {activeDetailTab === 'applications' && (
        <ApplicationList
          applications={applicationsData?.data || []}
          onViewApplication={(appId) =>
            console.log(`View application ${appId}`)
          }
        />
      )}
    </div>
  );
}
