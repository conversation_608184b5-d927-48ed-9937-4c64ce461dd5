import './globals.css';
import { NextIntlClientProvider } from 'next-intl';
import { getLocale, getMessages } from 'next-intl/server';

import { Hydration } from '@components/Hydration';
import { Body, ScrollableContent } from '@components/Layout';
import { ModalProvider } from '@components/ModalsComponents/context/ModalContext';
import { ModalLayout } from '@components/ModalsComponents/layout';
import Navbar from '@components/Navbar/Navbar';
import { QueryProvider } from '@components/QueryProvider';
import { Toaster } from '@components/ui/toaster';

import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Create Next App',
  description: 'Generated by create next app',
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  // Providing all messages to the client
  // side is the easiest way to get started
  const messages = await getMessages();

  return (
    <QueryProvider>
      <Hydration>
        <ModalProvider>
          <html lang={locale}>
            <Body>
              <NextIntlClientProvider messages={messages}>
                <ModalLayout />
                <Navbar />
                <Toaster />

                <ScrollableContent>{children}</ScrollableContent>
              </NextIntlClientProvider>
            </Body>
          </html>
        </ModalProvider>
      </Hydration>
    </QueryProvider>
  );
}
