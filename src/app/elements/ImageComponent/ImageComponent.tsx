import Image from 'next/image';

import { ImageComponentProps } from './types';

export function ImageComponent({
  src,
  alt,
  className = '',
  style,
  fill = false,
  width,
  height,
  priority = false,
  sizes,
  isAvatar = false,
  isLogo = false,
  objectFit = 'cover',
  onClick,
  placeholder,
  blurDataURL,
  unoptimized = false,
  quality,
  ...props
}: ImageComponentProps) {
  let imageSrc = src;
  if (
    !src?.startsWith('http') &&
    !src?.startsWith('data:') &&
    !src?.startsWith('/')
  ) {
    imageSrc = `${process.env.NEXT_PUBLIC_API_URL}/files/${src}`;
  }

  const defaultSizes =
    sizes ||
    (isAvatar
      ? '48px'
      : isLogo
        ? '(min-width: 768px) 124px, 100px'
        : '(min-width: 1024px) 25vw, (min-width: 768px) 33vw, 50vw');

  const defaultClassName = `
    ${objectFit === 'cover' ? 'object-cover' : objectFit === 'contain' ? 'object-contain' : ''}
    ${isAvatar ? 'rounded-full' : isLogo ? 'rounded-[20px]' : ''}
    ${fill ? 'absolute' : ''}
    backdrop-blur-default
    ${className}
  `.trim();

  return (
    <Image
      src={imageSrc}
      alt={alt}
      fill={fill}
      width={!fill ? width : undefined}
      height={!fill ? height : undefined}
      className={defaultClassName}
      style={style}
      priority={priority}
      sizes={defaultSizes}
      onClick={onClick}
      placeholder={placeholder}
      blurDataURL={blurDataURL}
      unoptimized={unoptimized}
      quality={quality}
      {...props}
    />
  );
}
