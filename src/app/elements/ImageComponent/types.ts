export type ImageComponentProps = {
  src: string;
  alt: string;
  className?: string;
  style?: React.CSSProperties;
  fill?: boolean;
  width?: number;
  height?: number;
  priority?: boolean;
  unoptimized?: boolean;
  quality?: number | `${number}`;
  sizes?: string;
  isAvatar?: boolean;
  isLogo?: boolean;
  objectFit?: 'cover' | 'contain' | 'fill';
  placeholder?: 'blur' | 'empty';
  blurDataURL?: string;
  onClick?: () => void;
};
