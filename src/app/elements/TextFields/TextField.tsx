import { Text } from '@app/elements/Texts';

import { Input as UIInput } from '@components/ui/input';

import { borderRadiusStyles, paddings, fontSizes } from '../_shared';
import {
  defaultTextFieldWrapper,
  textFieldMaxWidth,
  defaultTextFieldTitle,
  defaultTextFieldInput,
} from './constants';
import { TextFieldProps } from './types';

export function TextField({
  title,
  inputType = 'text',
  placeholder,
  input = '',
  setInput,
  borderRadius = 'extra',
  fullWidth = false,
  titleSize = 'sm',
  textSize = 'md',
  padding = 'md',
  className = '',
  step = '1',
  onClick,
  autoFocus = false,
}: TextFieldProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if ((e.key === 'Enter' || e.key === ' ') && onClick) {
      onClick();
    }
  };

  return (
    <div
      role="button"
      tabIndex={onClick ? 0 : undefined}
      onClick={() => onClick && onClick()}
      onKeyDown={handleKeyDown}
      className={`${fullWidth ? 'w-full' : textFieldMaxWidth} ${defaultTextFieldWrapper} ${borderRadiusStyles[borderRadius]} ${paddings[padding]} ${className} cursor-default`}
    >
      <Text className={`${defaultTextFieldTitle} ${fontSizes[titleSize]}`}>
        {title}
      </Text>
      <UIInput
        autoFocus={autoFocus}
        type={inputType}
        step={inputType === 'number' ? step : undefined}
        className={`${defaultTextFieldInput} ${fontSizes[textSize]} `}
        value={input}
        placeholder={placeholder}
        onChange={(e) => setInput && setInput(e.target.value)}
      />
    </div>
  );
}
