'use client';

import React, { forwardRef } from 'react';

import { Text } from '@app/elements/Texts';

import { Icons, IconName } from '@components/icons';

import { useMediaQuery } from '@hooks/useMediaQuery';

import { defaultTextFieldTitle } from './constants';
import { TextFieldReadOnlyProps } from './types';

const defaultTitleStyles = 'text-opacity-50';
const defaultTitleValueStyles = 'truncate text-white';

const defaultTextFielReadOnlyWrapper =
  'flex flex-col items-start gap-0.5 rounded-xl border border-tf-border px-3 py-2';

export const TextFieldReadOnly = forwardRef<
  HTMLDivElement,
  TextFieldReadOnlyProps
>(
  (
    {
      className,
      title,
      titleValue,
      textSize = 'sm',
      fullWidth = false,
      icon = null,
      iconSize = 'sm',
      iconStyle = 'normal',
      isDropdownOpen = false,
      toggleDropdown = () => {},
    },
    ref
  ) => {
    const IconComponent = icon ? Icons[icon as IconName] : null;

    const onKeyEvent = (e: React.KeyboardEvent<HTMLDivElement>) => {
      if (e.key === 'Enter' || e.key === ' ') {
        toggleDropdown();
      }
    };

    const renderAnotherLocationsText = () => {
      if (Array.isArray(titleValue)) {
        const { length } = titleValue;

        return (
          <Text fontSize="sm" isSpan className={`${defaultTextFieldTitle}`}>
            {`+ ${length - 1}`}
          </Text>
        );
      }
    };

    const getFormattedValue = (): React.ReactNode => {
      if (Array.isArray(titleValue)) {
        if (isDropdownOpen) {
          return titleValue[0];
        }
        if (titleValue.length > 1) {
          return (
            <div className="grid grid-cols-[1fr,auto] gap-2">
              <div className="truncate">{titleValue[0]}</div>{' '}
              {renderAnotherLocationsText()}
            </div>
          );
        }
        return titleValue[0];
      }
      return titleValue as string;
    };

    const formattedValue = getFormattedValue();

    return (
      <div
        ref={ref}
        className={`cursor-default ${fullWidth ? 'w-full' : 'max-w-tf'} ${defaultTextFielReadOnlyWrapper} ${className} relative`}
        onClick={toggleDropdown}
        onKeyDown={onKeyEvent}
        tabIndex={0}
        role="button"
      >
        <div className="flex w-full items-center justify-between">
          <div>
            <Text
              fontSize="sm"
              isSpan
              className={`${defaultTextFieldTitle} ${defaultTitleStyles}`}
            >
              {title}
            </Text>
            <Text
              fontSize={textSize || 'md'}
              className={`${defaultTitleValueStyles} w-full whitespace-normal break-all`}
            >
              {formattedValue}
            </Text>
          </div>
          <div>
            {icon &&
              Array.isArray(titleValue) &&
              titleValue.length > 1 &&
              IconComponent && (
                <IconComponent
                  size={iconSize}
                  className={`${iconStyle === 'overflow' ? 'mb-[-24px] max-h-[48px] min-h-[64px]' : 'min-w-[18px]'} ml-auto`}
                />
              )}
          </div>
        </div>
        {isDropdownOpen &&
          Array.isArray(titleValue) &&
          titleValue.length > 1 && (
            <ul className="absolute left-0 top-3/4 z-10 mt-2 w-full rounded-md bg-white text-sm text-black shadow-md">
              {titleValue.map((location, index) => (
                <li key={index} className="mt-0 px-3 py-1">
                  {location}
                </li>
              ))}
            </ul>
          )}
      </div>
    );
  }
);

TextFieldReadOnly.displayName = 'TextFieldReadOnly';
