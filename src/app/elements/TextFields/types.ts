import { IconName } from '@components/icons/IconWrapper';

import {
  BorderRadiusProps,
  FontSizesProps,
  IconSizesProps,
  PaddingsProps,
} from '../_shared';

export type TextFieldProps = {
  inputType?: 'text' | 'number' | 'password';
  title?: string;
  placeholder?: string;
  fullWidth?: boolean;
  borderRadius?: BorderRadiusProps;
  input?: string | string[];
  setInput?: React.Dispatch<React.SetStateAction<string>>;
  titleSize?: FontSizesProps;
  textSize?: FontSizesProps;
  padding?: PaddingsProps;
  className?: string;
  // eslint-disable-next-line react/no-unused-prop-types
  step?: string;
  // eslint-disable-next-line react/no-unused-prop-types
  onClick?: () => void;
  // eslint-disable-next-line react/no-unused-prop-types
  autoFocus?: boolean; // If true, this focuses our input and changes focus of the browser to it. Usefull in Modals
};

export type TextFieldAsTextProps = {
  title: string;
  text: string;
  fullWidth?: boolean;
  borderRadius?: BorderRadiusProps;
  titleSize?: FontSizesProps;
  textSize?: FontSizesProps;
  padding?: PaddingsProps;
  className?: string;
};

export type TextFieldReadOnlyProps = {
  className?: string;
  title: string;
  titleValue?: string | string[] | React.ReactNode;
  textSize?: FontSizesProps;
  fullWidth?: boolean;
  icon?: IconName | null;
  iconSize?: IconSizesProps;
  iconStyle?: 'normal' | 'overflow';
  isDropdownOpen?: boolean;
  toggleDropdown?: () => void;
};

export type TextFieldTextAreaProps = {
  title: string;
  placeholder?: string;
  value: string;
  setValue?: React.Dispatch<React.SetStateAction<string>>;
  fullWidth?: boolean;
  titleSize?: FontSizesProps;
  textSize?: FontSizesProps;
  padding?: PaddingsProps;
  className?: string;
  borderRadius?: BorderRadiusProps;
  autoFocus?: boolean;
  icon?: IconName | null;
  iconSize?: IconSizesProps;
  disabledUserActivity?: boolean;
  onClick?: () => void;
};

export type TextFieldTextAreaClickableProps = {
  title: string;
  placeholder?: string;
  fullWidth?: boolean;
  titleSize?: FontSizesProps;
  textSize?: FontSizesProps;
  padding?: PaddingsProps;
  className?: string;
  borderRadius?: BorderRadiusProps;
  value?: string[] | string;
  autoFocus?: boolean;
  icon?: IconName | null;
  iconSize?: IconSizesProps;
  // eslint-disable-next-line react/no-unused-prop-types
  onClick?: () => void;
  shouldResize?: boolean;
};

export type TextFieldTextAreaReadOnlyProps = {
  title: string;
  titleValue: string | string[];
  fullWidth?: boolean;
};

export type TextFieldWithIconProps = {
  icon?: IconName | null;
  iconSize?: IconSizesProps | number;
  iconStyle?: 'normal' | 'overflow';
  readOnly?: boolean;
  isLocationArray?: boolean;
} & TextFieldProps;
