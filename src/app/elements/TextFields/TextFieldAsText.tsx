import { Text } from '@app/elements/Texts';

import { borderRadiusStyles, paddings, fontSizes } from '../_shared';
import {
  defaultTextFieldWrapper,
  textFieldMaxWidth,
  defaultTextFieldTitle,
} from './constants';
import { TextFieldAsTextProps } from './types';

export function TextFieldAsText({
  title,
  text = '',
  borderRadius = 'extra',
  fullWidth = false,
  titleSize = 'sm',
  textSize = 'md',
  padding = 'md',
  className = '',
}: TextFieldAsTextProps) {
  return (
    <div
      className={`${fullWidth ? 'w-full' : textFieldMaxWidth} ${defaultTextFieldWrapper} ${borderRadiusStyles[borderRadius]} ${paddings[padding]} ${className} cursor-default`}
    >
      <Text className={`${defaultTextFieldTitle} ${fontSizes[titleSize]}`}>
        {title}
      </Text>
      <Text fontSize={textSize} variant="white" fontWeight="medium">
        {text}
      </Text>
    </div>
  );
}
