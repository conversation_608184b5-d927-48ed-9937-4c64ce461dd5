import { Text } from '@app/elements/Texts';

import {
  defaultTextFieldWrapper,
  textFieldMaxWidth,
  defaultTextFieldTitle,
} from './constants';
import { TextFieldTextAreaReadOnlyProps } from './types';

const defaultTextAreaReadOnlyText =
  ' w-full border-none bg-transparent p-0  font-semibold text-tf-text-content shadow-none resize-none overflow-hidden';

// Basic kind of text field area with title and titleValue for only read
export function TextFieldTextAreaReadOnly({
  title,
  titleValue,
  fullWidth = false,
}: TextFieldTextAreaReadOnlyProps) {
  return (
    <div
      className={`rounded-xl p-4 ${fullWidth ? 'w-full' : textFieldMaxWidth} ${defaultTextFieldWrapper}`}
    >
      <Text fontSize="sm" className={`${defaultTextFieldTitle} mb-1`}>
        {title}
      </Text>
      {Array.isArray(titleValue) && titleValue.length > 1 ? (
        <ul className="list-disc px-4 text-white">
          {titleValue.map((item, index) => (
            <li key={index}>
              <Text
                fontSize="sm"
                className={`${defaultTextAreaReadOnlyText} text-white`}
              >
                {item}
              </Text>
            </li>
          ))}
        </ul>
      ) : (
        <Text
          fontSize="sm"
          className={`${defaultTextAreaReadOnlyText} text-white`}
        >
          {titleValue}
        </Text>
      )}
    </div>
  );
}
