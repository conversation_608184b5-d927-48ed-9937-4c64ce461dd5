import { Text } from '@app/elements/Texts';

import { Icons, IconName } from '@components/icons';
import { Input as UIInput } from '@components/ui/input';

import { useMediaQuery } from '@hooks/useMediaQuery';

import { borderRadiusStyles, paddings, fontSizes, iconSizes } from '../_shared';
import {
  defaultTextFieldWrapper,
  textFieldMaxWidth,
  defaultTextFieldTitle,
  defaultTextFieldInput,
} from './constants';
import { TextFieldWithIconProps } from './types';

export function TextFieldWithIcon({
  title,
  inputType = 'text',
  input = '',
  placeholder,
  setInput,
  icon = null,
  iconSize = 'md',
  iconStyle = 'normal',
  titleSize = 'sm',
  textSize = 'md',
  fullWidth = false,
  borderRadius = 'extra',
  padding = 'md',
  className = '',
  autoFocus = false,
  readOnly = false,
  onClick,
  isLocationArray = false,
}: TextFieldWithIconProps) {
  const isMobile = useMediaQuery('(max-width: 768px)');

  const IconComponent = icon ? Icons[icon as IconName] : null;

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if ((e.key === 'Enter' || e.key === ' ') && onClick) {
      onClick();
    }
  };

  const renderAnotherLocationsText = () => {
    if (isLocationArray && Array.isArray(input) && input.length > 1) {
      const { length } = input;
      if (isMobile) {
        return (
          <Text fontSize="sm" isSpan className={`${defaultTextFieldTitle}`}>
            {`+ ${length - 1}`}
          </Text>
        );
      }
      return (
        <Text fontSize="xs" isSpan className={`${defaultTextFieldTitle}`}>
          {`+ ${length - 1} locations`}
        </Text>
      );
    }
    return null;
  };

  const getFormattedValue = (): React.ReactNode => {
    if (isLocationArray && Array.isArray(input)) {
      if (input.length > 1) {
        return (
          <>
            {input[0]} {renderAnotherLocationsText()}
          </>
        );
      }
      return input[0] || '';
    }
    return input;
  };

  const formattedValue = getFormattedValue();

  return (
    <div
      role="button"
      tabIndex={onClick ? 0 : undefined}
      onClick={() => onClick && onClick()}
      onKeyDown={handleKeyDown}
      className={`${defaultTextFieldWrapper} cursor-default select-none ${fullWidth ? 'w-full' : textFieldMaxWidth} $ relative flex h-max items-center justify-between gap-2 overflow-hidden ${borderRadiusStyles[borderRadius]} ${paddings[padding]} ${className}`}
    >
      {/* Title and content */}
      <div className="w-full">
        <Text className={`${defaultTextFieldTitle} ${fontSizes[titleSize]}`}>
          {title}
        </Text>

        {isLocationArray && Array.isArray(input) ? (
          <div className={`${defaultTextFieldInput} ${fontSizes[textSize]}`}>
            {formattedValue}
          </div>
        ) : (
          <UIInput
            autoFocus={autoFocus}
            type={inputType}
            className={`${defaultTextFieldInput} ${fontSizes[textSize]}`}
            value={typeof input === 'string' ? input : ''}
            placeholder={placeholder}
            onChange={(e) => setInput && setInput(e.target.value)}
            readOnly={readOnly}
          />
        )}
      </div>

      {icon && IconComponent && (
        <div
          role="button"
          tabIndex={0}
          className={`${iconStyle === 'overflow' ? 'absolute px-4' : 'static'} right-0 flex h-full cursor-pointer content-center items-center justify-center`}
        >
          <IconComponent
            size={iconSize}
            className={`
      ${
        iconStyle === 'overflow'
          ? `absolute right-5 max-h-[36px] min-h-[64px] w-auto pt-2 opacity-80 
           mobile:max-h-[48px] mobile:min-h-[80px]`
          : ''
      }
    `}
          />
        </div>
      )}

      {/* Dropdown dla lokalizacji */}
      {isLocationArray && Array.isArray(input) && input.length > 1 && (
        <ul className="absolute left-0 top-full z-10 mt-1 w-full rounded-md bg-white text-sm text-black shadow-md">
          {(input as string[])
            .slice(1)
            .map((location: string, index: number) => (
              <li
                key={index}
                className="cursor-pointer px-3 py-2 hover:bg-gray-100"
              >
                {location}
              </li>
            ))}
        </ul>
      )}
    </div>
  );
}
