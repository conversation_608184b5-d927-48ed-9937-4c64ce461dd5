// Description:
/* 
This file contains all variables or types that can be reused acros all /elements files 
We might move them later to other better places, but for now, it is fine
*/

// Define the type for icon sizes
export type IconSizesProps = 'xs' | 'sm' | 'md' | 'lg' | 'xl';

// Define the icon sizes object
export const iconSizes: Record<IconSizesProps, string> = {
  xs: 'h-[16px] w-[16px]',
  sm: 'h-[24px] w-[24px]',
  md: 'h-[28px] w-[28px]',
  lg: 'h-[32px] w-[32px]',
  xl: 'h-[36px] w-[36px]',
};

// Font / Text sizes
export type FontSizesProps = 'standard' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export const fontSizes: Record<FontSizesProps, string> = {
  standard: 'text-[12px]',
  xs: 'text-[14px] max-full:text-[14px] max-dekstop:[text-14px] max-laptop:text-[12px] max-tablet:text-[12px] max-mobile:text-[10px]',
  sm: 'text-[18px] max-full:text-[16px] max-dekstop:[text-16px] max-laptop:text-[14px] max-tablet:text-[14px] max-mobile:text-[12px]',
  md: 'text-[20px] max-full:text-[18px] max-dekstop:[text-18px] max-laptop:text-[16px] max-tablet:text-[16px] max-mobile:text-[14px]',
  lg: 'text-[22px] max-full:text-[20px] max-dekstop:[text-20px] max-laptop:text-[18px] max-tablet:text-[18px] max-mobile:text-[16px]',
  xl: 'text-[24px] max-full:text-[22px] max-dekstop:[text-22px] max-laptop:text-[20px] max-tablet:text-[20px] max-mobile:text-[18px]',
};

export type FontWeightProps =
  | 'light'
  | 'normal'
  | 'medium'
  | 'semibold'
  | 'bold';

// Elements paddings
// These paddings are more on X then Y
export type PaddingsProps = 'none' | 'xs' | 'sm' | 'md' | 'lg' | 'xl';
export const paddings: Record<PaddingsProps, string> = {
  none: 'p-0',
  xs: 'px-3 py-1',
  sm: 'px-3 py-2    max-tablet:px-2   max-tablet:py-1.5',
  md: 'px-4 py-3    max-tablet:px-3   max-tablet:py-2',
  lg: 'px-5 py-3.5  max-tablet:px-4   max-tablet:py-2.5',
  xl: 'px-6 py-4    max-tablet:px-5   max-tablet:py-3',
};

// Elements paddings that have both sides equal (Usefull for buttons with icons)
// They have to scale exactly the same as py in 'paddings'
export const equalPaddings: Record<PaddingsProps, string> = {
  none: 'p-0',
  xs: 'p-1',
  sm: 'p-2      max-tablet:p-1.5',
  md: 'p-3      max-tablet:p-2',
  lg: 'p-3.5    max-tablet:p-2.5',
  xl: 'p-4      max-tablet:p-3',
};

// border radius of elements
export type BorderRadiusProps = 'default' | 'rounded' | 'extra' | 'max';
export const borderRadiusStyles = {
  default: 'rounded-md ',
  rounded: 'rounded-[16px] max-tablet:rounded-[10px]',
  extra: 'rounded-[20px] max-tablet:rounded-[14px]',
  max: 'rounded-[50px]',
};
