const defaultRadioWrapper =
  'flex w-full cursor-pointer items-center justify-between gap-4 rounded-2xl border px-5 py-3 transition duration-default ease-in-out hover:border-radio-checked-border hover:bg-radio-hover-bg ';

const defaultRadioTitle =
  'border-none p-0 text-lg text-white font-medium text-tf-text-content outline-none placeholder:text-tf-text-content';

const defaultRadioAdditionalText = 'text-tf-text-title font-bold text-base';

const defaultRadioCircle =
  'flex min-h-[40px] min-w-[40px] items-center justify-center rounded-full ';

const radioMaxWidth = 'max-w-radio';

const radioChecked = 'border-radio-checked-border';
const radioUnchecked = 'border-radio-border';

export {
  defaultRadioWrapper,
  defaultRadioTitle,
  defaultRadioAdditionalText,
  defaultRadioCircle,
  radioMaxWidth,
  radioChecked,
  radioUnchecked,
};
