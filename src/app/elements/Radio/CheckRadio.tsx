import React from 'react';

import { Text } from '@app/elements/Texts';

import { CheckIcon } from '@components/icons';

import {
  defaultRadioWrapper,
  defaultRadioTitle,
  defaultRadioAdditionalText,
  defaultRadioCircle,
  radioMaxWidth,
  radioChecked,
  radioUnchecked,
} from './constans';
import { CheckRadioProps } from './types';

function RadioCircle({ isChecked }: { isChecked: boolean }) {
  return (
    <div
      className={`${defaultRadioCircle} ${isChecked && 'bg-radio-hover-bg'}`}
    >
      {isChecked && <CheckIcon className="p-1" size={28} />}
    </div>
  );
}

export function CheckRadio({
  id,
  text,
  additionalText = '',
  isChecked,
  onChange,
  fullWidth = false,
}: CheckRadioProps) {
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Enter' || e.key === ' ') {
      onChange(id);
    }
  };

  return (
    <div
      role="radio"
      aria-checked={isChecked}
      tabIndex={0}
      className={` ${defaultRadioWrapper} ${fullWidth ? 'w-full' : radioMaxWidth} ${isChecked ? radioChecked : radioUnchecked}`}
      onClick={() => onChange(id)}
      onKeyDown={handleKeyDown}
    >
      <div className="flex w-full items-center gap-2 overflow-hidden">
        <Text className={`${defaultRadioTitle}`}>{text}</Text>

        <Text className={`${defaultRadioAdditionalText}`}>
          {additionalText}
        </Text>
      </div>

      <RadioCircle isChecked={isChecked} />
    </div>
  );
}
