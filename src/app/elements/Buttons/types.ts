import { IconName } from '@components/icons/IconWrapper';

import {
  BorderRadiusProps,
  FontSizesProps,
  IconSizesProps,
  PaddingsProps,
} from '../_shared';

export type ButtonProps = {
  children?: React.ReactNode;
  fullWidth?: boolean;
  variant?: 'primary' | 'secondary' | 'link';
  padding?: PaddingsProps;
  fontSize?: FontSizesProps;
  borderRadius?: BorderRadiusProps;
  className?: string;
} & React.ButtonHTMLAttributes<HTMLButtonElement>;

export type ButtonAsIconProps = ButtonProps & {
  buttonIcon: IconName;
  iconSize?: IconSizesProps;
  onClick?: () => void;
};

export type ButtonCrossProps = ButtonProps & {
  iconSide?: 'left' | 'right';
};
