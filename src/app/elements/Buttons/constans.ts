const baseStyles =
  'min-w-max h-min text-xl font-semibold duration-default shadow-none focus-visible:outline-btn-secondary-border focus-visible:ring-0';

// Button colors
const colorStyles = {
  primary: {
    base: 'bg-btn-primary-bg text-btn-primary-color',
    border: 'border border-transparent',
    hover: 'hover:bg-btn-primary-hover-bg',
  },
  secondary: {
    base: 'bg-btn-secondary-bg text-btn-secondary-color',
    border: 'border border-btn-secondary-border',
    hover: 'hover:bg-btn-secondary-hover-bg hover:border-btn-secondary-bg',
  },
  link: {
    base: 'text-tf-text-title underline underline-offset-4 bg-transparent',
    border: 'border-none',
    hover: 'hover:text-white',
  },
};

const crossIconStyles = 'hover:scale-default hover:duration-default';

export { baseStyles, colorStyles, crossIconStyles };
