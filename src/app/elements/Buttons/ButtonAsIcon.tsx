import React from 'react';

import { Icons, IconName } from '@components/icons';
import { Button as UIButton } from '@components/ui/button';

import { borderRadiusStyles, equalPaddings } from '../_shared';
import { colorStyles, baseStyles } from './constans';
import { ButtonAsIconProps } from './types';

export function ButtonAsIcon({
  fullWidth = false,
  variant = 'primary',
  padding = 'md',
  borderRadius = 'rounded',
  buttonIcon = 'close',
  iconSize = 'md',

  className = '',
  onClick,
  ...additionalProps
}: ButtonAsIconProps) {
  const selectedColorStyles = colorStyles[variant];
  const IconComponent = Icons[buttonIcon as IconName];

  const buttonClassName = `
      ${baseStyles}
      ${equalPaddings[padding]}
      ${borderRadiusStyles[borderRadius]}
      ${selectedColorStyles.base}
      ${selectedColorStyles.border}
      ${selectedColorStyles.hover}
      ${fullWidth ? 'w-full' : 'w-max'}
      ${className}
    `;

  return (
    <UIButton
      className={`${buttonClassName}`}
      {...additionalProps}
      onClick={onClick}
    >
      <IconComponent size={iconSize} />
    </UIButton>
  );
}
