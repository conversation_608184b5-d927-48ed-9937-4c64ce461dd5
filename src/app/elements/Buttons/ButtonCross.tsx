import React from 'react';

import { CloseIcon } from '@components/icons';
import { Button as UIButton } from '@components/ui/button';

import { borderRadiusStyles, fontSizes, paddings } from '../_shared';
import { colorStyles, baseStyles, crossIconStyles } from './constans';
import { ButtonCrossProps } from './types';

export function ButtonCross({
  children,
  fullWidth = false,
  variant = 'primary',
  padding = 'md',
  fontSize = 'md',
  borderRadius = 'rounded',
  iconSide = 'left',
  className = '',
  ...additionalProps
}: ButtonCrossProps) {
  const selectedColorStyles = colorStyles[variant];

  // Combine all styles dynamically
  const buttonClassName = `
      ${baseStyles}
      ${paddings[padding]}
      ${borderRadiusStyles[borderRadius]}
      ${selectedColorStyles.base}
      ${selectedColorStyles.border}
      ${selectedColorStyles.hover}
      ${fullWidth ? 'w-full' : 'w-max'}
      ${className}
    `;

  return (
    <UIButton className={`${buttonClassName} flex gap-4`} {...additionalProps}>
      {iconSide === 'left' && <CloseIcon className={crossIconStyles} />}
      <span className={`${fontSizes[fontSize]}`}>{children}</span>
      {iconSide === 'right' && <CloseIcon className={crossIconStyles} />}
    </UIButton>
  );
}
