import React from 'react';

import { Button as UIButton } from '@components/ui/button';

import { borderRadiusStyles, fontSizes, paddings } from '../_shared';
import { colorStyles, baseStyles } from './constans';
import { ButtonProps } from './types';

export function Button({
  children,
  fullWidth = false,
  variant = 'primary',
  padding = 'md',
  fontSize = 'md',
  borderRadius = 'rounded',
  className = '',
  ...additionalProps
}: ButtonProps) {
  const selectedColorStyles = colorStyles[variant];

  // Combine all styles dynamically
  const buttonClassName = `
      ${baseStyles}
      ${paddings[padding]}
      ${borderRadiusStyles[borderRadius]}
      ${selectedColorStyles.base}
      ${selectedColorStyles.border}
      ${selectedColorStyles.hover}
      ${fullWidth ? 'w-full' : 'w-max'}
      ${className}
    `;

  return (
    <UIButton className={buttonClassName} {...additionalProps}>
      <span className={`${fontSizes[fontSize]}`}>{children}</span>
    </UIButton>
  );
}
