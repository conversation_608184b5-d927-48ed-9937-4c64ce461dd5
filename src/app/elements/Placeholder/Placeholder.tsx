import { EditPenIcon } from '@components/icons';

import { PlaceholderProps } from './types';

export function Placeholder({
  iconOnly = false,
  sizeIconOnly = 35,
  size = 35,
}: PlaceholderProps) {
  if (iconOnly) {
    return <EditPenIcon size={sizeIconOnly} />;
  }

  return (
    <div className="flex size-full items-center justify-center rounded-2xl bg-[#FFFFFF80]">
      <EditPenIcon size={size} />
    </div>
  );
}
