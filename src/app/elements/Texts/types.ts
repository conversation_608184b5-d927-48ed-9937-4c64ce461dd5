import { FontSizesProps, FontWeightProps } from '../_shared';

import { textVariant } from './constants';

export type TextAlign = 'left' | 'center' | 'right' | 'justify';

export type TextVariant = {
  [key: string]: {
    base: string;
  };
};

export type TextProps = {
  children: React.ReactNode;
  align?: TextAlign;
  fontSize?: FontSizesProps;
  fontWeight?: FontWeightProps;
  className?: string;
  variant?: keyof typeof textVariant;
  isSpan?: boolean;
  value?: string;
};

export type TLinkProps = {
  children: React.ReactNode;
  href?: string;
};
