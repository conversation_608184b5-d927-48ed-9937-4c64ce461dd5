import React from 'react';

import { TextProps } from './types';
import { fontSizes } from '../_shared';
import { textVariant } from './constants';

export function Text({
  children,
  align = 'left',
  fontSize = 'standard',
  fontWeight = 'normal',
  className,
  variant = 'primary',
  isSpan,
  value,
}: TextProps) {
  const fontSizeClass = fontSizes[fontSize];

  return isSpan ? (
    <span className={`text-${align} ${fontSizeClass} font-normal ${className}`}>
      {children || value}
    </span>
  ) : (
    <div
      className={`text-${align} ${fontSizeClass} font-${fontWeight} ${className} ${textVariant[variant].base}`}
    >
      {children}
    </div>
  );
}
