@tailwind base;
@tailwind components;
@tailwind utilities;

body,
html {
  width: 100%;
  height: 100%;

  margin: 0;
  padding: 0;
}

:root {
  /* Global  */
  --layout-bg: #242424; /* Background of our app */

  /* text-fileds.tsx */
  --tf-border: #ffffff88; /* Default border */
  --tf-border-focus: #fff; /* Focus border */
  --tf-text-title: #ffffff88; /* Title text */
  --tf-text-content: #fff; /* Content text */

  /* radio.tsx  */
  --radio-border: #ffffff88; /* Border */
  --radio-checked-border: #fff; /* Checked radio button border */
  --radio-hover-bg: #ffffff11; /* Background when hover */
  --radio-primary: #fff; /* Primary text color */
  --radio-additional: #ffffff88; /* Additional text color */

  /* buttons.tsx */
  /* Primary  */
  --btn-primary-bg: #ffffff30;
  --btn-primary-color: #fff;
  --btn-primary-hover-bg: #ffffff50;

  /* Secondary  */
  --btn-secondary-bg: transparent;
  --btn-secondary-border: #ffffff88;
  --btn-secondary-color: #fff;
  --btn-secondary-hover-bg: #ffffff50;
}

@layer base {
  :root {
    /* shadcn colors  */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
  .loader {
    width: 8vmax;
    height: 8vmax;
    border-right: 4px solid #ffffff;
    border-radius: 100%;
    animation: spinRight 0.8s linear infinite;

    &:before,
    &:after {
      content: '';
      width: 6vmax;
      height: 6vmax;
      display: block;
      position: absolute;
      top: calc(50% - 3vmax);
      left: calc(50% - 3vmax);
      border-left: 3px solid #ffffff;
      border-radius: 100%;
      animation: spinLeft 0.8s linear infinite;
    }

    &:after {
      width: 4vmax;
      height: 4vmax;
      top: calc(50% - 2vmax);
      left: calc(50% - 2vmax);
      border: 0;
      border-right: 2px solid #ffffff;
      animation: none;
    }
  }

  .auto-height-textarea {
    height: auto !important;
    overflow: hidden !important;
    min-height: 1.5em;
  }

  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px transparent inset !important;
    -webkit-text-fill-color: white !important;
    background-color: transparent !important;
    color: white !important;
    caret-color: white !important;
    transition:
      background-color 1000s ease-in-out 0s,
      color 0s;
  }

  @keyframes spinLeft {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(720deg);
    }
  }

  @keyframes spinRight {
    from {
      transform: rotate(360deg);
    }
    to {
      transform: rotate(0deg);
    }
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }
  /* Hide scrollbar for IE, Edge and Firefox */
  .no-scrollbar {
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
  }
}
