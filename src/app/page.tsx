import { Suspense } from 'react';

import { headers } from 'next/headers';
import { redirect } from 'next/navigation';

import { getTranslations } from 'next-intl/server';

import { FirstColumn, SecondColumn } from '@components/Layout';
import LoaderWithBackground from '@components/Loader/LoaderWithBackground';
import OfferOverview from '@components/Offers/OfferOverview/OfferOverview';
import { OffersList } from '@components/Offers/OffersList/OffersList';
import { OfferDetailedProps } from '@components/Offers/types';

import { getJobOffer } from '@actions/getJobOffer';
import { getJobOffers, JobOffersFilters } from '@actions/getJobOffers';

export default async function Home({
  searchParams,
}: {
  searchParams: Promise<{
    activeOffer: string;
    viewport: 'mobile' | 'desktop';
    categoryId?: string;
    position?: string;
    minSalary?: string;
    maxSalary?: string;
  }>;
}) {
  const t = await getTranslations('Home');

  const searchParamsObject = await searchParams;

  // Przygotowanie parametrów filtrowania z URL
  const filters = {
    currentPage: 1,
    categoryId: searchParamsObject.categoryId
      ? Number(searchParamsObject.categoryId)
      : undefined,
    position: searchParamsObject.position,
    minSalary: searchParamsObject.minSalary
      ? Number(searchParamsObject.minSalary)
      : undefined,
    maxSalary: searchParamsObject.maxSalary
      ? Number(searchParamsObject.maxSalary)
      : undefined,
  };

  const offersData = await getJobOffers(filters);

  if (!offersData) {
    return <main className="flex size-full grow gap-4">{t('noOffers')}</main>;
  }

  const activeOfferId = Number(searchParamsObject.activeOffer);
  const isMobile = searchParamsObject.viewport === 'mobile';
  const isValidActiveOfferId = Number.isInteger(activeOfferId);

  if (!isValidActiveOfferId && !isMobile && offersData.data) {
    const newParams = new URLSearchParams(searchParamsObject);
    newParams.set('activeOffer', offersData.data.items[0].id.toString());
    return redirect(`?${newParams.toString()}`);
  }

  const isInitialLoad = headers().get('accept')?.includes('text/html');

  const loadedJobOffer =
    isInitialLoad && isValidActiveOfferId
      ? (await getJobOffer(activeOfferId)).data
      : null;

  let offers = offersData.data;

  // active offer is not on the first page of job offers
  if (
    loadedJobOffer &&
    offers &&
    !offers.items.find((offer) => offer.id === loadedJobOffer.id)
  ) {
    offers = {
      ...offers,
      items: [loadedJobOffer, ...offers.items],
    };
  }

  return (
    <main className="flex size-full grow gap-4">
      {activeOfferId ? (
        <>
          <FirstColumn>
            <OffersList
              initialOffersData={offers ? { data: offers } : null}
              activeOffer={activeOfferId}
              isInitialLoad={isInitialLoad}
              filters={filters}
            />
          </FirstColumn>
          <SecondColumn>
            <Suspense fallback={<LoaderWithBackground />} key={activeOfferId}>
              <OfferOverviewWithFetch
                activeOfferId={activeOfferId}
                preloadedOffer={loadedJobOffer}
              />
            </Suspense>
          </SecondColumn>
        </>
      ) : (
        <OffersList
          initialOffersData={offers ? { data: offers } : null}
          activeOffer={activeOfferId}
          isInitialLoad={isInitialLoad}
          filters={filters}
        />
      )}
    </main>
  );
}

async function OfferOverviewWithFetch({
  activeOfferId,
  preloadedOffer,
}: {
  activeOfferId: number;
  preloadedOffer: OfferDetailedProps | null;
}) {
  const t = await getTranslations('Home');

  if (preloadedOffer?.id === activeOfferId) {
    return <OfferOverview offer={preloadedOffer} />;
  }

  const offerData = await getJobOffer(activeOfferId);

  if (!offerData.data) {
    return <main className="flex size-full grow gap-4">{t('noOffer')}</main>;
  }

  return <OfferOverview offer={offerData.data} />;
}
