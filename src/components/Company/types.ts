import { OfferDetailedProps } from '@components/Offers/types';

export interface CompanyBase {
  id: number;
  name: string;
  headline: string;
  logo: string;
}

export interface JobOffer {
  id: number;
  position: string;
  minSalary: number;
  maxSalary: number;
  contractType: string;
  currency: string;
  createdDate: string;
  assignedTo: CompanyBase;
  locations: string[];
}

export interface CompanyDetails {
  id: number;
  company: CompanyBase;
  background: string;
  website: string;
  creationDate: string;
  location: string;
  aboutCompany: string;
  jobOffers: JobOffer[];
}

export interface CompanyDataProps {
  name: string;
  website: string;
  size: string;
  aboutUs: string;
  logo?: string;
}

export interface CompanyProfileProps {
  id: number;
  name: string;
  website: string;
  size: string;
  aboutUs: string;
  logo?: string;
}

export type CompanyTab = 'profile' | 'active' | 'expired';

export interface CompanyTabOption {
  id: CompanyTab;
  label: string;
  count?: number;
}

export interface CompanyTabsProps {
  activeTab?: CompanyTab;
  onTabChange?: (tab: CompanyTab) => void;
  activeOffersCount?: number;
  expiredOffersCount?: number;
  onBackClick?: () => void;
  selectedOffer?: JobOffer | null;
  applicationCount?: number;
  activeDetailTab?: 'offer' | 'applications';
  onDetailTabChange?: (tab: 'offer' | 'applications') => void;
}

export interface NavigationTabsProps {
  tabs?: CompanyTabOption[];
  activeTab?: CompanyTab;
  onTabChange?: (tab: CompanyTab) => void;
  onBackClick?: () => void;
  activeOffersCount?: number;
  expiredOffersCount?: number;
}

export interface NavigationDetailsTabsProps {
  onBackClick?: () => void;
  applicationCount?: number;
  activeDetailTab?: 'offer' | 'applications';
  onDetailTabChange?: (tab: 'offer' | 'applications') => void;
}

export interface OfferIndicatorProps {
  tab: CompanyTabOption;
}

// Do mapowania danych z API na istniejące komponenty
export interface MappedOffer {
  CompanyName: string;
  JobPosition: string;
  SalaryRange: string;
  OfferCountry: string;
  OfferCity: string;
  OfferImage: string;
  CreatedDate?: string;
}

// Helper do konwersji JobOffer na MappedOffer
export const mapJobOfferToMappedOffer = (offer: JobOffer): MappedOffer => {
  return {
    CompanyName: offer.assignedTo.name,
    JobPosition: offer.position,
    SalaryRange: `${offer.minSalary} - ${offer.maxSalary} ${offer.currency}`,
    OfferCountry: offer.locations?.[0]?.split(',')[1]?.trim() || 'Polska',
    OfferCity: offer.locations?.[0]?.split(',')[0]?.trim() || 'Warszawa',
    OfferImage: offer.assignedTo.logo,
    CreatedDate: offer.createdDate,
  };
};

export const mapJobOfferToOfferDetailedProps = (
  offer: JobOffer
): OfferDetailedProps => {
  const locationText = offer.locations?.[0] || '';
  const [city = '', province = ''] = locationText
    .split(',')
    .map((part) => part.trim());

  const locationObject = {
    id: 1,
    city: {
      id: 1,
      name: city || 'Warszawa',
      province: {
        id: 1,
        name: province || 'Mazowieckie',
        cities: [''] as [string],
      },
    },
    address: '',
  };

  const aboutCompany =
    (offer as any).aboutCompany || 'Informacje o firmie nie są dostępne';

  return {
    id: offer.id,
    position: offer.position,
    assignedTo: {
      id: offer.assignedTo.id,
      name: offer.assignedTo.name,
      logo: offer.assignedTo.logo,
      headline: offer.assignedTo.headline,
    },
    minSalary: offer.minSalary.toString(),
    maxSalary: offer.maxSalary.toString(),
    currency: offer.currency,
    locations: [locationObject] as [any],
    contractType: offer.contractType,

    createdDate: offer.createdDate,

    aboutCompany: aboutCompany,
    ourExpectations: 'Zrobić zakupy',
    yourDailyTasks: 'Sprzątać',
    whatWeOffers: 'Karta multisport',

    createdBy: {
      uuid: '',
      email: '',
      firstName: '',
      lastName: '',
      headline: '',
      avatar: '',
    },

    category: {
      id: 1,
      name: 'IT',
      subcategories: [''] as [string],
      offers: [''] as [string],
    },
  };
};

export interface CompanyOffersProps {
  offers: JobOffer[];
  onOpenOffer: (offer: JobOffer) => void;
  variant: 'active' | 'expired';
}
