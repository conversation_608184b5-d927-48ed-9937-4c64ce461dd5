'use client';

import { useRouter, useSearchParams } from 'next/navigation';

import { useTranslations } from 'next-intl';

import { borderRadiusStyles } from '@app/elements/_shared';

import { useCompany } from '@actions/hooks/useCompany';

import CompanyOffers from './components/CompanyOffers';
import CompanyProfile from './components/CompanyProfile';
import { CompanyTabs } from './components/CompanyTabs';
import { CompanyDetails, CompanyTab, JobOffer } from './types';

export function CompanyView({
  companyData: initialCompanyData,
}: {
  companyData?: CompanyDetails | null;
}) {
  const router = useRouter();
  const t = useTranslations('Company');
  const { data } = useCompany(
    initialCompanyData?.company.id,
    initialCompanyData
  );
  const companyData = data?.data || initialCompanyData;

  const searchParams = useSearchParams();
  const activeTab = (searchParams.get('activeTab') || 'profile') as CompanyTab;

  const handleBackToHomePage = () => {
    router.push('/');
  };

  const filterOffers = (
    offers: JobOffer[] = []
  ): { active: JobOffer[]; expired: JobOffer[] } => {
    const monthAgo = new Date();
    monthAgo.setMonth(monthAgo.getMonth() - 1);

    return {
      active: offers.filter((offer) => new Date(offer.createdDate) > monthAgo),
      expired: offers.filter(
        (offer) => new Date(offer.createdDate) <= monthAgo
      ),
    };
  };

  const { active: activeOffers, expired: expiredOffers } = filterOffers(
    companyData?.jobOffers || []
  );

  if (!companyData) {
    return (
      <div className="flex justify-center p-10">{t('companyNotFound')}</div>
    );
  }

  const handleOpenOffer = (offer: JobOffer) => {
    router.push(`/company/${companyData.company.id}/offer/${offer.id}`);
  };

  const setActiveTab = (newTab: CompanyTab) => {
    const newParams = new URLSearchParams(searchParams);
    newParams.set('activeTab', newTab);
    return router.replace(`?${newParams.toString()}`);
  };

  return (
    <div
      className={`mx-auto flex size-full max-w-4xl flex-col gap-4 ${borderRadiusStyles.extra}`}
    >
      <CompanyTabs
        activeTab={activeTab}
        onTabChange={setActiveTab}
        activeOffersCount={activeOffers?.length}
        expiredOffersCount={expiredOffers?.length}
        onBackClick={handleBackToHomePage}
      />

      {activeTab === 'profile' && (
        <CompanyProfile
          id={companyData.company.id}
          name={companyData.company.name || ''}
          website={companyData.website || ''}
          size="300-500 pracowników"
          aboutUs={companyData.aboutCompany || ''}
          logo={companyData.company.logo}
        />
      )}
      {activeTab === 'active' && (
        <CompanyOffers
          offers={activeOffers}
          onOpenOffer={handleOpenOffer}
          variant="active"
        />
      )}
      {activeTab === 'expired' && (
        <CompanyOffers
          offers={expiredOffers}
          onOpenOffer={handleOpenOffer}
          variant="expired"
        />
      )}
    </div>
  );
}
