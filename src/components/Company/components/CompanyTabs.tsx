import { CompanyTabsProps } from '../types';
import { NavigationDetailsTabs } from './CompanyNavigation/NavigationDetailsTabs';
import { NavigationTabs } from './CompanyNavigation/NavigationTabs';

export function CompanyTabs({
  activeTab,
  onTabChange,
  activeOffersCount = 0,
  expiredOffersCount = 0,
  onBackClick,
  selectedOffer = null,
  applicationCount = 0,
  activeDetailTab = 'offer',
  onDetailTabChange,
}: CompanyTabsProps) {
  return (
    <div className="w-full">
      {selectedOffer ? (
        <NavigationDetailsTabs
          onBackClick={onBackClick}
          applicationCount={applicationCount}
          activeDetailTab={activeDetailTab}
          onDetailTabChange={onDetailTabChange}
        />
      ) : (
        <NavigationTabs
          activeTab={activeTab}
          onTabChange={onTabChange}
          onBackClick={onBackClick}
          activeOffersCount={activeOffersCount}
          expiredOffersCount={expiredOffersCount}
        />
      )}
    </div>
  );
}
