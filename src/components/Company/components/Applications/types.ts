import { Application } from '@actions/getApplications';

export interface Company {
  id: number;
  name: string;
  headline: string;
  logo: string;
}

export interface JobOffer {
  id: number;
  position: string;
  minSalary: number;
  maxSalary: number;
  contractType: string;
  currency: string;
  createdDate: string;
  assignedTo: Company;
  locations: string[];
}

export interface User {
  uuid: string;
  email: string;
  firstName: string;
  lastName: string;
  headline: string;
  avatar: string;
}

export interface ApplicationItem extends Application {
  status?: 'new' | 'old';
  viewed?: boolean;
}

export interface ApplicationListProps {
  applications: ApplicationItem[];
  onViewApplication?: (applicationId: number) => void;
}

export interface ApplicationItemProps {
  application: ApplicationItem;
  onView: (applicationId: number) => void;
}
