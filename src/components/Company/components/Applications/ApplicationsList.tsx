'use client';

import React, { useState, useEffect } from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { Text } from '@app/elements/Texts';

import { ApplicationItem } from './ApplicationItem';
import { useDownloadApplicationsZip } from './hooks';
import { ApplicationListProps, ApplicationItem as AppItem } from './types';

export function ApplicationList({
  applications = [],
  onViewApplication,
}: ApplicationListProps) {
  const t = useTranslations('Applications');
  const { downloadAllApplications, isDownloading } =
    useDownloadApplicationsZip();

  const [applicationList, setApplicationsList] = useState<AppItem[]>([]);

  useEffect(() => {
    const appsWithStatus = applications.map((app) => ({
      ...app,
      status: app.status || 'new',
      viewed: app.viewed || false,
    }));
    setApplicationsList(appsWithStatus);
  }, [applications]);

  const handleViewApplication = (applicationId: number) => {
    const application = applicationList.find((app) => app.id === applicationId);

    // open cv in new window
    if (application?.cv) {
      const cvUrl = `${process.env.NEXT_PUBLIC_API_URL}/files/${application.cv}`;
      window.open(cvUrl, '_blank');
    }

    // Update application status to viewed/old
    setApplicationsList((prev) =>
      prev.map((app) =>
        app.id === applicationId ? { ...app, status: 'old', viewed: true } : app
      )
    );

    onViewApplication?.(applicationId);
  };


  const handleDownloadAllApplications = () => {
    downloadAllApplications(applicationList);
  };

  if (applications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center p-10">
        <Text fontSize="lg" className="text-white text-opacity-70">
          Brak aplikacji na tą ofertę pracy
        </Text>
      </div>
    );
  }

  return (
    <>
      <div className="mb-4 overflow-y-auto">
        <div className="space-y-4">
          {applicationList.map((app) => (
            <ApplicationItem
              key={app.id}
              application={app}
              onView={handleViewApplication}
            />
          ))}
        </div>
      </div>
      <div className="mt-2 flex justify-end">
        <Button
          disabled={isDownloading}
          onClick={handleDownloadAllApplications}
          className="w-auto"
        >
          Pobierz wszystkie aplikacje
        </Button>
      </div>
    </>
  );
}
