import React from 'react';

import { Button } from '@app/elements/Buttons';
import { ImageComponent } from '@app/elements/ImageComponent';
import { Text } from '@app/elements/Texts';

import { ApplicationItemProps } from './types';

export function ApplicationItem({
  application: app,
  onView,
}: ApplicationItemProps) {
  const fullName = `${app.user.firstName} ${app.user.lastName}`;

  const handleButtonClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onView(app.id);
  };

  const handleApplicationClick = () => {
    onView(app.id);
  };

  return (
    <div
      className="relative flex cursor-pointer items-center justify-between rounded-[30px] bg-white/10 p-4 transition-colors hover:bg-white/20"
      onClick={handleApplicationClick}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          handleApplicationClick();
        }
      }}
      role="button"
      tabIndex={0}
    >
      <div className="flex items-center gap-3">
        {app.user.avatar ? (
          <ImageComponent
            src={app.user.avatar}
            alt={fullName}
            width={48}
            height={48}
            className="rounded-full"
            isAvatar
          />
        ) : (
          <div className="flex size-12 items-center justify-center rounded-full bg-gray-500">
            <Text variant="white" fontSize="md" className="font-bold">
              {app.user.firstName.charAt(0)}
            </Text>
          </div>
        )}
        <div>
          <div className="flex items-center gap-2">
            <Text variant="white" fontSize="md" fontWeight="bold">
              {fullName}
            </Text>

            <div className="flex items-center gap-1 rounded-3xl bg-[#ffffff20] px-2">
              <Text variant="white" fontSize="xs" className="text-opacity-80">
                {new Date(app.createdDate).toLocaleDateString()}
              </Text>
            </div>
            {app.status === 'new' && (
              <span className="rounded-full bg-[#42FF00] px-1.5 text-xs text-[#005200]">
                Nowa
              </span>
            )}
          </div>
          <Text variant="white" fontSize="sm" className="text-opacity-70">
            {app.user.email}
          </Text>
          <Text variant="white" fontSize="sm" className="text-opacity-70">
            {/* co z numerem? czy wgl chcemy nr jesli tak to skad z profilu uzytkownika czy dodatkowe pole przy aplikowaniu? */}
            +48 500 435 643
          </Text>
        </div>
      </div>
      {/* przy wpinaniu backendu mozliwosc pobrania konrektnej aplikacji */}
      <Button
        onClick={handleButtonClick}
        className="absolute right-4 top-1/2 hidden -translate-y-1/2 rounded-3xl px-5 py-2 tablet:block"
      >
        Otwórz
      </Button>
    </div>
  );
}
