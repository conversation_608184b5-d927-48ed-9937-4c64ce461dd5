import { useState } from 'react';

import { toast, ToastIconVariant } from '@hooks/use-toast';

import { ApplicationItem } from './types';

export function useDownloadApplicationsZip() {
  const [isDownloading, setIsDownloading] = useState(false);

  const downloadAllApplications = async (applications: ApplicationItem[]) => {
    if (applications.length === 0) return;

    setIsDownloading(true);

    try {
      const JSZip = (await import('jszip')).default;
      const zip = new JSZip();

      let completedDownloads = 0;
      let errorCount = 0;

      const downloadPromises = applications.map(async (app) => {
        try {
          const cvUrl = `${process.env.NEXT_PUBLIC_API_URL}/files/${app.cv}`;
          const response = await fetch(cvUrl);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
          }

          const blob = await response.blob();

          // const firstName = (app.user.firstName || 'Nieznane').replace(
          //           //   /[^a-zA-Z0-9]/g,
          //           //   ''
          //           // );
          //           // const lastName = (app.user.lastName || 'Nazwisko').replace(
          //           //   /[^a-zA-Z0-9]/g,
          //           //   ''
          //           // );
          //           // const fileName = `${firstName}_${lastName}_ID${app.id}.pdf`;

          const fileName = `${app.user.email}.pdf`;

          completedDownloads += 1;

          return { fileName, blob };
        } catch (error) {
          errorCount += 1;
          return null;
        }
      });

      const results = await Promise.all(downloadPromises);

      // Dodaj pliki do ZIP
      let addedFiles = 0;
      results.forEach((result) => {
        if (result) {
          zip.file(result.fileName, result.blob);
          addedFiles += 1;
        }
      });

      if (addedFiles === 0) {
        if (errorCount > 0) {
          const toastInstance = toast({
            iconVariant: ToastIconVariant.ERROR,
            description: 'Błąd podczas pobierania aplikacji',
          });
          setTimeout(() => {
            toastInstance.dismiss();
          }, 2500);
        }
        return;
      }

      // Wygeneruj i pobierz ZIP
      const zipBlob = await zip.generateAsync({
        type: 'blob',
        compression: 'DEFLATE',
        compressionOptions: { level: 6 },
      });

      const url = window.URL.createObjectURL(zipBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `Lista_aplikacji_${new Date().toISOString().split('T')[0]}.zip`;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        description: 'Błąd podczas tworzenia zip',
      });
    } finally {
      setTimeout(() => {
        setIsDownloading(false);
      }, 3000);
    }
  };

  return {
    downloadAllApplications,
    isDownloading,
  };
}
