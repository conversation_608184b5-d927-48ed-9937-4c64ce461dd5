import { useTranslations } from 'next-intl';

import { Button, ButtonAsIcon } from '@app/elements/Buttons';

import {
  CompanyTabOption,
  NavigationTabsProps,
} from '@components/Company/types';

import { OfferIndicator } from './OfferIndicator';

export function NavigationTabs({
  activeTab,
  onTabChange,
  onBackClick,
  activeOffersCount = 0,
  expiredOffersCount = 0,
}: NavigationTabsProps) {
  const t = useTranslations('Company');

  const tabs: CompanyTabOption[] = [
    { id: 'profile', label: t('companyProfile') },
    { id: 'active', label: t('activeOffers'), count: activeOffersCount },
    { id: 'expired', label: t('expiredOffers'), count: expiredOffersCount },
  ];
  return (
    <div className="flex w-full">
      <div className="flex shrink-0 items-center">
        {/* <ButtonAsIcon
          buttonIcon="arrowLeft"
          iconSize="md"
          className="h-full !px-5"
          borderRadius="extra"
          onClick={onBackClick}
        /> */}
      </div>
      <div className="sm:p-1 flex w-full min-w-0 shrink items-center gap-2 rounded-xl bg-layout-box-bg p-1.5 backdrop-blur-default">
        <div className="no-scrollbar w-full overflow-x-auto">
          <div className="flex min-w-max gap-2">
            {tabs.map((tab) => (
              <Button
                key={tab.id}
                onClick={() => onTabChange && onTabChange(tab.id)}
                className={`
                    flex-1 justify-center whitespace-nowrap
                    ${
                      activeTab === tab.id
                        ? 'cursor-default bg-btn-primary-hover-bg text-white'
                        : 'bg-transparent'
                    }
                  `}
              >
                <OfferIndicator tab={tab} />
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
