import { Text } from '@app/elements/Texts';

import { CompanyTab, OfferIndicatorProps } from '@components/Company/types';

export function OfferIndicator({ tab }: OfferIndicatorProps) {
  const getCountBgColor = (tabId: CompanyTab) => {
    switch (tabId) {
      case 'active':
        return 'bg-[#42FF00]';
      case 'expired':
        return 'bg-[#FF4242]';
      default:
        return null;
    }
  };

  return (
    <div className="flex items-center gap-1.5">
      <Text variant="white" fontSize="sm" className="text-center">
        {tab.label}
      </Text>
      {tab.count !== undefined && (
        <div className="flex items-center gap-1.5 rounded-3xl bg-[#ffffff20] px-2 ">
          <span
            className={`block size-2 rounded-full ${getCountBgColor(tab.id)}`}
          />
          <Text variant="white" fontSize="xs" className="text-center">
            {tab.count}
          </Text>
        </div>
      )}
    </div>
  );
}
