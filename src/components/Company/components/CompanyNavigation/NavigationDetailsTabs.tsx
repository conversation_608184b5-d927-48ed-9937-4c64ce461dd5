import { useTranslations } from 'next-intl';

import { Button, ButtonAsIcon } from '@app/elements/Buttons';
import { Text } from '@app/elements/Texts';

import { NavigationDetailsTabsProps } from '@components/Company/types';

export function NavigationDetailsTabs({
  onBackClick,
  applicationCount = 0,
  activeDetailTab = 'offer',
  onDetailTabChange,
}: NavigationDetailsTabsProps) {
  const t = useTranslations('Company');

  const detailsTabs = [
    {
      id: 'offer',
      label: t('yourOffer'),
      isMain: activeDetailTab === 'offer',
      onClick: () => onDetailTabChange && onDetailTabChange('offer'),
    },
    {
      id: 'applications',
      label: t('applicationList'),
      isMain: activeDetailTab === 'applications',
      count: applicationCount,
      onClick: () => onDetailTabChange && onDetailTabChange('applications'),
    },
  ];

  return (
    <div className="flex w-full">
      <div className="flex shrink-0 items-center">
        {/* <ButtonAsIcon
          buttonIcon="arrowLeft"
          iconSize="md"
          className="h-full !px-5"
          borderRadius="extra"
          onClick={onBackClick}
        /> */}
      </div>
      <div className="sm:p-1 flex w-full min-w-0 shrink items-center gap-2 rounded-xl bg-layout-box-bg p-1.5">
        <div className="no-scrollbar w-full overflow-x-auto">
          <div className="flex min-w-max gap-2">
            {detailsTabs.map((detailTab) => (
              <Button
                key={detailTab.id}
                onClick={detailTab.onClick || (() => {})}
                className={`
                    flex-1 justify-center whitespace-nowrap
                    ${detailTab.isMain ? 'cursor-default bg-btn-primary-hover-bg text-white' : 'bg-transparent'}
                  `}
              >
                <div className="flex items-center gap-1.5">
                  <Text variant="white" fontSize="sm" className="text-center">
                    {detailTab.label}
                  </Text>
                  {detailTab.count !== undefined && (
                    <div className="flex items-center gap-1.5 rounded-3xl bg-[#ffffff20] px-2">
                      <span className="block size-2 rounded-full bg-[#42FF00]" />
                      <Text
                        variant="white"
                        fontSize="xs"
                        className="text-center"
                      >
                        {detailTab.count}
                      </Text>
                    </div>
                  )}
                </div>
              </Button>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
