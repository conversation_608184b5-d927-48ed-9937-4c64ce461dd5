'use client';

import {
  TOAST_REMOVE_DELAY,
  ToastIconVariant,
  useToast,
} from '@/hooks/use-toast';
import {
  Toast,
  ToastClose,
  ToastDescription,
  ToastProvider,
  ToastTitle,
  ToastViewport,
} from '@/components/ui/toast';

import { DangerIcon, InfoCircleIcon, SuccessIcon } from '@components/icons';

const Icon = ({ variant }: { variant?: ToastIconVariant }) => {
  switch (variant) {
    case ToastIconVariant.SUCCESS:
      return <SuccessIcon className="mr-4" />;
    case ToastIconVariant.ERROR:
      return <DangerIcon className="mr-4" />;
    default:
      return <InfoCircleIcon className="mr-4" />;
  }
};

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider swipeDirection="down" duration={TOAST_REMOVE_DELAY}>
      {toasts.map(function ({
        id,
        title,
        description,
        action,
        iconVariant,
        ...props
      }) {
        return (
          <Toast key={id} {...props}>
            <Icon variant={iconVariant} />
            <div className="grid gap-2">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && (
                <ToastDescription>{description}</ToastDescription>
              )}
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport />
    </ToastProvider>
  );
}
