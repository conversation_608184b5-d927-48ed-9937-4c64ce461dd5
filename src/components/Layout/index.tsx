import { PropsWithChildren, FC } from 'react';

import { Roboto } from 'next/font/google';

// Main font Roboto
const roboto = Roboto({
  subsets: ['latin'],
  weight: ['100', '300', '400', '500', '700'],
});

export const Body: FC<PropsWithChildren> = ({ children }) => (
  <body
    className={`flex size-full min-h-lvh flex-col overscroll-none bg-medical-bg bg-cover bg-fixed bg-no-repeat px-6 pt-0 max-laptop:px-4 max-laptop:pt-0 max-tablet:px-2 max-tablet:pt-0 ${roboto.className} `}
  >
    {children}
  </body>
);

export const ScrollableContent: FC<PropsWithChildren> = ({ children }) => (
  <div className="relative flex-1 pb-6 pt-[132px] max-full:pt-[130px] max-desktop:pt-[221px] max-tablet:pt-[197px]">
    {children}
  </div>
);

export const SecondColumn: FC<PropsWithChildren> = ({ children }) => (
  <div className="fixed left-1/2 right-6 top-0 h-full overflow-auto pb-[16px] pt-[132px] max-full:pt-[130px] max-desktop:pt-[221px] max-laptop:left-6 max-tablet:pt-[197px]">
    {children}
  </div>
);

export const FirstColumn: FC<PropsWithChildren> = ({ children }) => (
  <div className="flex w-1/2 flex-col pe-6 max-laptop:hidden">{children}</div>
);
