import { FormEvent, useCallback, useState } from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';
import { Text, TLink } from '@app/elements/Texts';

import { registerUser } from '@actions/registerUser';
import { toast, ToastIconVariant } from '@hooks/use-toast';

export function ModalRegister() {
  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');
  const [passwordRepeat, setPasswordRepeat] = useState<string>('');
  const t = useTranslations('Modals');
  const tToasts = useTranslations('Toasts');

  const handleSubmit = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      try {
        const response = await registerUser({
          email,
          password,
          confirmPassword: passwordRepeat,
        });

        if (response?.userUuid) {
          window.location.reload();
        }
      } catch (error) {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
        });
      }
    },
    [email, password, passwordRepeat, tToasts]
  );

  return (
    <form
      onSubmit={handleSubmit}
      autoComplete="off"
      className="flex flex-col gap-4"
    >
      <TextField
        title={t('email')}
        input={email}
        setInput={setEmail}
        placeholder={t('emailExample')}
        borderRadius="rounded"
      />

      <TextField
        title={t('password')}
        input={password}
        setInput={setPassword}
        placeholder={t('passwordPlaceholder')}
        borderRadius="rounded"
        inputType="password"
      />

      <TextField
        title={t('repeatPassword')}
        input={passwordRepeat}
        setInput={setPasswordRepeat}
        placeholder={t('repeatPasswordPlaceholder')}
        borderRadius="rounded"
        inputType="password"
      />

      <div className="p-2">
        <Text align="center" fontSize="md">
          {t.rich('acceptTerms', {
            link: (chunks) => <TLink href="">{chunks}</TLink>,
          })}
        </Text>
      </div>

      <Button borderRadius="rounded" fullWidth padding="lg">
        {t('register')}
      </Button>
    </form>
  );
}
