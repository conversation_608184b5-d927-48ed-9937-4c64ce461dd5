import React, { useRef, useState } from 'react';

import { useQueryClient } from '@tanstack/react-query';

import { Button } from '@app/elements/Buttons';
import { TextField, TextFieldWithIcon } from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import SimpleModalLayout from '@components/SimpleModal/Layout';

import { useOfferApplicationGuest } from '@actions/hooks/useOfferApplicationGuest';
import { toast, ToastIconVariant } from '@hooks/use-toast';

import { ApplicationModalProps } from './types';

export function ApplicationModal({
  closeModal,
  onSuccess,
  offer,
}: ApplicationModalProps) {
  const offerApplicationGuest = useOfferApplicationGuest();
  const queryClient = useQueryClient();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
  });

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setSelectedFile(file);
    } else {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        title: 'Proszę wybrać plik PDF',
      });
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const handleInputChange =
    (field: 'firstName' | 'lastName' | 'email') =>
    (value: React.SetStateAction<string>) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));
    };

  const handleSave = async () => {
    if (!formData.firstName.trim()) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        title: 'Proszę wprowadzić imię',
      });
      return;
    }

    if (!formData.lastName.trim()) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        title: 'Proszę wprowadzić nazwisko',
      });
      return;
    }

    if (!formData.email.trim()) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        title: 'Proszę wprowadzić adres email',
      });
      return;
    }

    if (!selectedFile) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        title: 'Proszę dodać plik CV',
      });
      return;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        title: 'Proszę wprowadzić poprawny adres email',
      });
      return;
    }

    await offerApplicationGuest({
      ...formData,
      jobOffer: offer.id,
      file: selectedFile,
    });

    onSuccess();

    await queryClient.invalidateQueries({
      queryKey: ['getApplications', offer.id],
    });
  };

  return (
    <SimpleModalLayout closeModal={closeModal}>
      <div className="relative">
        <span className="absolute top-1 h-16 w-1.5 rounded-md bg-slate-50 max-laptop:h-12" />
        <div className="ml-3.5">
          <Text fontSize="lg">{offer.assignedTo.name}</Text>
          <Text fontWeight="semibold" variant="white" fontSize="xl">
            {offer.position}
          </Text>
        </div>
      </div>
      <TextField
        title="Imię"
        input={formData.firstName}
        setInput={handleInputChange('firstName')}
        placeholder="Podaj imię"
        fullWidth
      />
      <TextField
        title="Nazwisko"
        input={formData.lastName}
        setInput={handleInputChange('lastName')}
        placeholder="Podaj nazwisko"
        fullWidth
      />

      <TextField
        title="Adres email"
        input={formData.email}
        setInput={handleInputChange('email')}
        placeholder="Podaj email"
        fullWidth
      />

      <div
        className="w-full cursor-pointer"
        role="button"
        tabIndex={0}
        onClick={triggerFileInput}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            triggerFileInput();
          }
        }}
      >
        <TextFieldWithIcon
          title="CV"
          input={selectedFile ? selectedFile.name : ''}
          setInput={() => {}}
          placeholder="Dodaj CV"
          fullWidth
          icon="upload"
          iconStyle="overflow"
          className="pointer-events-none"
        />

        <input
          type="file"
          ref={fileInputRef}
          onChange={handleFileChange}
          accept=".pdf"
          className="hidden"
        />
      </div>
      <div className="px-2">
        <Text align="center">
          Klikając przycisk "Aplikuj" oświadczasz, że zapoznałeś się z
          regulaminem serwisu i akceptujesz jego wytyczne. Twoje dane zostaną
          zapisane w naszych bazach, a na adres email otrzymasz potwierdzenie
          wraz z linkiem, który pozwoli nadać hasło do przeglądania Twoich
          aplikacji.
        </Text>
      </div>

      <Button fullWidth className="mt-3" onClick={handleSave}>
        Aplikuj
      </Button>
    </SimpleModalLayout>
  );
}
