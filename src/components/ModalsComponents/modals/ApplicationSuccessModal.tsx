import React from 'react';

import { Button } from '@app/elements/Buttons';
import { Text } from '@app/elements/Texts';

import SimpleModalLayout from '@components/SimpleModal/Layout';

import { ApplicationSuccesModalProps } from './types';

export function ApplicationSuccesModal({
  closeModal,
}: ApplicationSuccesModalProps) {
  return (
    <SimpleModalLayout closeModal={closeModal}>
      <div className="relative flex flex-col gap-3.5">
        <span className="absolute top-1 h-16 w-1.5 rounded-md bg-slate-50 max-laptop:h-12" />
        <div className="ml-3.5">
          <Text fontWeight="semibold" variant="white" fontSize="xl">
            Pomyślnie Aplikowano!
          </Text>
          <Text fontSize="lg">Dane zostały zapisane</Text>
        </div>
        <Button
          onClick={() =>
            console.log('redirect to register page with autofill email?')
          }
          fullWidth
          disabled
        >
          Z<PERSON><PERSON><PERSON><PERSON> konto dla podanego maila
        </Button>
        <Button onClick={closeModal} variant="secondary" fullWidth>
          Kontynuuj Przeglądanie
        </Button>
      </div>
    </SimpleModalLayout>
  );
}
