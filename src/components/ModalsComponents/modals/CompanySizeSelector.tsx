import { useState } from 'react';

import { Button } from '@app/elements/Buttons';
import { CheckRadio } from '@app/elements/Radio/CheckRadio';

import { COMPANY_SIZE_OPTIONS } from '../constans';
import { CompanySizeSelectorProps } from './types';

export function CompanySizeSelector({
  currentSize,
  onSizeSelect,
  onClose,
}: CompanySizeSelectorProps) {
  const [selectedOption, setSelectedOption] = useState(() => {
    const matchedSize = COMPANY_SIZE_OPTIONS.find(
      (size) => size.text === currentSize
    );
    return matchedSize ? { id: matchedSize.id, text: matchedSize.text } : null;
  });

  const handleSave = () => {
    if (selectedOption) {
      onSizeSelect(selectedOption.text);
      onClose();
    }
  };

  return (
    <>
      <div className="space-y-4">
        {COMPANY_SIZE_OPTIONS.map(({ id, text, additionalText }) => (
          <CheckRadio
            key={id}
            id={id}
            text={text}
            additionalText={additionalText}
            isChecked={selectedOption?.id === id}
            onChange={() => setSelectedOption({ id, text })}
          />
        ))}
      </div>
      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleSave}
      >
        Zapisz
      </Button>
    </>
  );
}
