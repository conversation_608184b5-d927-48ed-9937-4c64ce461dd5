import { FormEvent, useCallback, useState } from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { login } from '@actions/server/login';
import { toast, ToastIconVariant } from '@hooks/use-toast';

import { useModal } from '../context/ModalContext';

export function ModalLogin() {
  const { openModal } = useModal();
  const t = useTranslations('Modals');
  const tToasts = useTranslations('Toasts');

  const [email, setEmail] = useState<string>('');
  const [password, setPassword] = useState<string>('');

  const handleSubmit = useCallback(
    async (e: FormEvent<HTMLFormElement>) => {
      e.preventDefault();

      const response = await login({
        email,
        password,
      });

      if (response?.userUuid) {
        window.location.reload();
      } else {
        toast({
          iconVariant: ToastIconVariant.ERROR,
          title: tToasts('error'),
          description: response?.errorMessage,
        });
      }
    },
    [email, password, tToasts]
  );

  return (
    <form
      onSubmit={handleSubmit}
      autoComplete="off"
      className="flex flex-col gap-4"
    >
      <TextField
        title={t('email')}
        input={email}
        setInput={setEmail}
        placeholder={t('emailExample')}
        borderRadius="rounded"
      />

      <TextField
        title={t('password')}
        input={password}
        setInput={setPassword}
        placeholder={t('passwordPlaceholder')}
        borderRadius="rounded"
        inputType="password"
      />

      <Button borderRadius="rounded" fullWidth padding="lg" type="submit">
        {t('logIn')}
      </Button>

      <div className="p-2">
        <Text align="center" fontSize="md">
          {t('forgotPassword')}{' '}
          <Button
            variant="link"
            onClick={() =>
              openModal('forgot_password', ['login', 'signin'], 'login')
            }
            padding="none"
          >
            {t('resetPassword')}
          </Button>
        </Text>
      </div>
    </form>
  );
}
