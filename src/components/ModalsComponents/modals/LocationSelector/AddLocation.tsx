import { useState } from 'react';

import { useQueryClient } from '@tanstack/react-query';

import { Button } from '@app/elements/Buttons';
import { CheckRadio } from '@app/elements/Radio/CheckRadio';
import { TextField } from '@app/elements/TextFields';

import { useCities } from '@actions/hooks/useCities';
import { useCreateLocation } from '@actions/hooks/useCreateLocation';

export function AddLocation({ onClose }: { onClose: (id?: string) => void }) {
  const { data, isLoading } = useCities();
  const createLocation = useCreateLocation();
  const queryClient = useQueryClient();
  const cities = data?.data || [];

  const [addressInput, setAddressInput] = useState('');

  const [city, setCity] = useState<string | null>(null);

  const handleSaveAddress = async () => {
    if (!city) return;

    await createLocation({
      city: Number(city),
      address: addressInput,
    });

    await queryClient.invalidateQueries({
      queryKey: ['getLocations'],
    });

    onClose(city);
  };

  if (isLoading)
    return (
      <div className="flex h-80 w-full items-center justify-center">
        <div className="loader" />
      </div>
    );

  return (
    <div className="space-y-4">
      {cities.map(({ id, name }) => (
        <CheckRadio
          key={id}
          id={id}
          text={name}
          isChecked={city === id}
          onChange={() => setCity(id)}
        />
      ))}

      <TextField
        className="rounded-xl"
        input={addressInput}
        setInput={setAddressInput}
        padding="lg"
        textSize="sm"
        placeholder="Podaj adres (opcjonalnie)"
      />

      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleSaveAddress}
        disabled={!city}
      >
        Dodaj
      </Button>

      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        variant="secondary"
        onClick={() => onClose()}
      >
        Wróć
      </Button>
    </div>
  );
}
