import { useState } from 'react';

import { Button } from '@app/elements/Buttons';
import { CheckRadio } from '@app/elements/Radio/CheckRadio';

import { typeOfContractOptions } from '../constans';
import { useModal } from '../context/ModalContext';

export type TypeOfContractKey = keyof typeof typeOfContractOptions;

export function ModalTypeOfContract() {
  const { handleVariableModalSave, modalDefaultValue } = useModal();
  const defaultKey = modalDefaultValue || 'contract_uop';
  const [selectedKey, setSelectedKey] = useState<string>(defaultKey);

  return (
    <>
      <div className="space-y-4">
        {Object.entries(typeOfContractOptions).map(([key, value]) => (
          <CheckRadio
            key={key}
            id={key}
            text={value}
            isChecked={selectedKey === key}
            onChange={() => setSelectedKey(key)}
          />
        ))}
      </div>

      <Button
        borderRadius="rounded"
        fullWidth
        onClick={() =>
          handleVariableModalSave?.({
            key: selectedKey,
            value:
              typeOfContractOptions[selectedKey as TypeOfContractKey] ||
              selectedKey,
          })
        }
      >
        Zapisz
      </Button>
    </>
  );
}
