import React from 'react';

import { Button } from '@app/elements/Buttons';

import { CategorySelector, CategorySelection } from './CategorySelector';

interface ModalCategoryProps {
  defaultValue?: CategorySelection | null;
  onSave: (selection: CategorySelection | null) => void;
}

export function ModalCategory({ defaultValue, onSave }: ModalCategoryProps) {
  const handleCategorySelect = (selection: CategorySelection) => {
    onSave(selection);
  };

  const handleClear = () => {
    onSave(null);
  };

  return (
    <div className="flex h-full max-h-[650px] flex-col">
      <CategorySelector
        selectedCategory={defaultValue}
        onCategorySelect={handleCategorySelect}
        fullWidth
      />

      {defaultValue && (
        <div className="shrink-0 p-4">
          <Button borderRadius="rounded" onClick={handleClear} fullWidth>
            <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
          </Button>
        </div>
      )}
    </div>
  );
}
