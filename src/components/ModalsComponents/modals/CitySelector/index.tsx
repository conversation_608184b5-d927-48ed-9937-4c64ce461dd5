import { useState } from 'react';

import { Button } from '@app/elements/Buttons';
import { CheckRadio } from '@app/elements/Radio/CheckRadio';

import { useCities } from '@actions/hooks/useCities';

export function CitySelector({
  onSelect,
  currentLocation,
}: {
  onSelect: (id?: string) => void;
  currentLocation?: string;
}) {
  const { data } = useCities();
  const cities = data?.data || [];

  const [selectedLocation, setSelectedLocation] = useState(currentLocation);

  const handleLocationClick = (id: string) => {
    setSelectedLocation(selectedLocation === id ? undefined : id);
  };

  const handleSave = () => {
    onSelect(selectedLocation);
  };

  return (
    <>
      <div className="space-y-4">
        {cities.map(({ id, name }) => (
          <CheckRadio
            key={id}
            id={id}
            text={name}
            isChecked={selectedLocation === id}
            onChange={handleLocationClick}
          />
        ))}

        {cities.length === 0 && (
          <div className="py-4 text-center text-white text-opacity-60">
            Nie znaleziono lokalizacji
          </div>
        )}
      </div>

      <div className="mt-4">
        <Button
          borderRadius="rounded"
          fullWidth
          padding="lg"
          onClick={handleSave}
        >
          Zapisz
        </Button>
      </div>
    </>
  );
}
