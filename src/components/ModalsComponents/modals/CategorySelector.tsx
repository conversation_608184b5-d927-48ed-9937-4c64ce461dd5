'use client';

import React, { useState, useEffect } from 'react';

import { CheckRadio } from '@app/elements/Radio';
import { TextField } from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { ArrowDropDownIcon } from '@components/icons';
import Loader from '@components/Loader/Loader';

import { useCategories } from '@actions/hooks/useCategories';

export interface Subcategory {
  id: number;
  name: string;
}

export interface Category {
  id: number;
  name: string;
  subcategories: Subcategory[];
}

export interface CategorySelection {
  category: Category;
  subcategory: Subcategory | null; // null oznacza wybór całej kategorii
}

interface CategorySelectorProps {
  selectedCategory?: CategorySelection | null;
  onCategorySelect: (selection: CategorySelection) => void;
  fullWidth?: boolean;
  className?: string;
}

export function CategorySelector({
  selectedCategory,
  onCategorySelect,
}: CategorySelectorProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategory, setExpandedCategory] = useState<number | null>(
    selectedCategory?.category.id || null
  );

  const categoriesData = useCategories();
  const categories = categoriesData?.data?.data || [];
  const { isLoading } = categoriesData;

  useEffect(() => {
    if (selectedCategory?.category.id) {
      setExpandedCategory(selectedCategory.category.id);
    }
  }, [selectedCategory]);

  const filteredCategories = categories.filter((category: Category) => {
    const hasSubcategories =
      category.subcategories && category.subcategories.length > 0;

    if (!hasSubcategories) {
      return false;
    }

    return (
      category.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      category.subcategories?.some((sub: Subcategory) =>
        sub.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    );
  });

  const sortedCategories = [...filteredCategories].sort(
    (a: Category, b: Category) => {
      if (selectedCategory?.category.id === a.id) return -1;
      if (selectedCategory?.category.id === b.id) return 1;
      return 0;
    }
  );

  const handleCategoryToggle = (categoryId: number) => {
    if (expandedCategory === categoryId) {
      setExpandedCategory(null);
    } else {
      setExpandedCategory(categoryId);
    }
  };

  const handleSubcategorySelect = (
    category: Category,
    subcategory: Subcategory
  ) => {
    const selection = { category, subcategory };
    onCategorySelect(selection);
  };

  const handleCategorySelect = (category: Category) => {
    // Wybór całej kategorii - subcategory będzie null
    const selection = { category, subcategory: null };
    onCategorySelect(selection);
  };

  const renderSearchInput = () => (
    <div className="shrink-0 p-1">
      <TextField
        className="rounded-xl"
        input={searchTerm}
        setInput={setSearchTerm}
        padding="lg"
        textSize="sm"
        placeholder="Wyszukaj specjalizację"
      />
    </div>
  );

  const renderSubcategories = (category: Category) => {
    if (!category.subcategories || category.subcategories.length === 0) {
      return null;
    }

    const isExpanded = expandedCategory === category.id;

    return (
      <div
        className={`overflow-hidden transition-all duration-300 ease-in-out ${
          isExpanded
            ? 'max-h-full pb-1 pt-2 opacity-100'
            : 'max-h-0 py-0 opacity-0'
        }`}
      >
        <div className="space-y-3 pl-5">
          {/* Opcja "Wszystko w kategorii" jako pierwsza */}
          <CheckRadio
            key={`category-${category.id}-all`}
            id={`category-${category.id}-all`}
            text={`Wszystko w "${category.name}"`}
            isChecked={
              selectedCategory?.category.id === category.id &&
              selectedCategory?.subcategory === null
            }
            onChange={() => handleCategorySelect(category)}
          />

          {/* Poszczególne podkategorie */}
          {category.subcategories.map((subcategory: Subcategory) => (
            <CheckRadio
              key={subcategory.id}
              id={`category-${category.id}-sub-${subcategory.id}`}
              text={subcategory.name}
              isChecked={selectedCategory?.subcategory?.id === subcategory.id}
              onChange={() => handleSubcategorySelect(category, subcategory)}
            />
          ))}
        </div>
      </div>
    );
  };

  const renderCategoryItem = (category: Category) => {
    const isExpanded = expandedCategory === category.id;

    return (
      <div
        key={category.id}
        className="space-y-1 transition-all duration-300 ease-in-out"
      >
        <div
          onClick={() => handleCategoryToggle(category.id)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault();
              handleCategoryToggle(category.id);
            }
          }}
          role="button"
          tabIndex={0}
          className={`flex w-full cursor-pointer items-center justify-between gap-4 rounded-2xl border p-5 transition-all duration-300 ease-in-out hover:border-radio-checked-border hover:bg-radio-hover-bg ${
            isExpanded ? 'border-radio-checked-border bg-radio-hover-bg' : ''
          }`}
        >
          <Text fontSize="md" className="font-medium text-white">
            {category.name}
          </Text>
          <ArrowDropDownIcon
            size="sm"
            className={`text-white/70 transition-all duration-300 ease-in-out ${
              isExpanded ? 'rotate-180' : 'rotate-0'
            }`}
          />
        </div>
        {renderSubcategories(category)}
      </div>
    );
  };

  const renderCategoriesList = () => {
    if (isLoading) return <Loader />;

    if (sortedCategories.length === 0 && searchTerm) {
      return (
        <div className="px-4 py-8 text-center">
          <Text fontSize="sm" className="text-white/60">
            Nie znaleziono specjalizacji dla "{searchTerm}"
          </Text>
        </div>
      );
    }

    return (
      <div className="space-y-2.5">
        {sortedCategories.map((category: Category) =>
          renderCategoryItem(category)
        )}
      </div>
    );
  };

  return (
    <>
      {renderSearchInput()}
      <div className="scrollbar-hide flex-1 overflow-y-auto px-1 pt-3">
        {renderCategoriesList()}
      </div>
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </>
  );
}
