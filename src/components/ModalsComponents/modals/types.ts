import { Dispatch, SetStateAction } from 'react';

import { OfferDetailedProps } from '@components/Offers/types';

export interface ApplicationModalProps {
  closeModal: () => void;
  onSuccess: () => void;
  offer: OfferDetailedProps;
}

export interface ApplicationSuccesModalProps {
  closeModal: () => void;
}
export interface CompanySizeSelectorProps {
  currentSize: string;
  onSizeSelect: Dispatch<SetStateAction<string>>;
  onClose: () => void;
}

export interface LocationSelectorProps {
  onSelect: (locations: string[]) => void;
  selectedLocations?: string[];
}

export interface ModalAboutUsProps {
  defaultValue?: string;
  onSave?: (value: string) => void;
}

export interface CompanyFormData {
  name: string;
  website: string;
  size: string;
  aboutUs: string;
}

export interface FilterData {
  category: string;
  location?: string;
  salary: {
    from: string;
    to: string;
  };
}
