import { Dispatch, SetStateAction, useState } from 'react';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';

import { useModal } from '@components/ModalsComponents/context/ModalContext';
import PriceRange from '@components/PriceRange/PriceRange';

import { useLocations } from '@actions/hooks/useLocations';

import { CitySelector } from './CitySelector';
import { FilterData } from './types';

const initialFilterData: FilterData = {
  category: '',
  location: '',
  salary: {
    from: '',
    to: '',
  },
};

export function ModalFilter() {
  const {
    openNestedModal,
    isNestedModalOpen,
    currentNestedModalType,
    closeNestedModal,
  } = useModal();
  const { data } = useLocations();
  const locations = data?.data || [];

  const [filterData, setFilterData] = useState<FilterData>(initialFilterData);

  const getFieldUpdater =
    (
      field: keyof Omit<FilterData, 'salary'>
    ): Dispatch<SetStateAction<string>> =>
    (value) => {
      const newValue =
        typeof value === 'function' ? value(filterData[field] || '') : value;
      setFilterData((prev) => ({ ...prev, [field]: newValue }));
    };

  const getSalaryUpdater =
    (type: 'from' | 'to'): Dispatch<SetStateAction<string>> =>
    (value) => {
      const newValue =
        typeof value === 'function' ? value(filterData.salary[type]) : value;

      setFilterData((prev) => ({
        ...prev,
        salary: {
          ...prev.salary,
          [type]: newValue,
        },
      }));
    };

  // Specjalna funkcja obsługi dla LocationSelector
  const handleLocationSelect = (location?: string) => {
    setFilterData((prev) => ({ ...prev, location }));

    closeNestedModal();
  };

  const handleClear = () => {
    setFilterData(initialFilterData);
  };

  const handleSearch = () => {
    // To do
  };

  if (isNestedModalOpen && currentNestedModalType === 'city') {
    return (
      <CitySelector
        currentLocation={filterData.location}
        onSelect={handleLocationSelect}
      />
    );
  }

  const location = (() => {
    if (!filterData.location) return '';

    return (
      locations.find(({ id }) => id === filterData.location)?.city.name || ''
    );
  })();

  return (
    <>
      <TextField
        title="Kategoria"
        input={filterData.category}
        setInput={getFieldUpdater('category')}
        placeholder="Wybierz specjalizację"
        borderRadius="rounded"
      />

      <TextField
        title="Lokalizacja"
        input={location}
        setInput={getFieldUpdater('location')}
        placeholder="Wybierz lokalizację"
        borderRadius="rounded"
        onClick={() => openNestedModal('city')}
      />

      <PriceRange
        salaryFrom={filterData.salary.from}
        setSalaryFrom={getSalaryUpdater('from')}
        salaryTo={filterData.salary.to}
        setSalaryTo={getSalaryUpdater('to')}
      />

      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleSearch}
      >
        Wyszukaj
      </Button>

      <Button
        variant="secondary"
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleClear}
      >
        Wyczyść
      </Button>
    </>
  );
}
