import { Dispatch, SetStateAction, useState, useEffect } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useQueryClient } from '@tanstack/react-query';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';

import { useModal } from '@components/ModalsComponents/context/ModalContext';
import PriceRange from '@components/PriceRange/PriceRange';

import { useLocations } from '@actions/hooks/useLocations';

import { CategorySelector, CategorySelection } from './CategorySelector';
import { CitySelector } from './CitySelector';
import { FilterData } from './types';

const initialFilterData: FilterData = {
  category: null,
  location: '',
  salary: {
    from: '',
    to: '',
  },
};

export function ModalFilter() {
  const {
    openNestedModal,
    isNestedModalOpen,
    currentNestedModalType,
    closeNestedModal,
    closeModal,
  } = useModal();
  const { data } = useLocations();
  const locations = data?.data || [];
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const [filterData, setFilterData] = useState<FilterData>(initialFilterData);

  // Inicjalizacja filtrów z URL przy pierwszym załadowaniu
  useEffect(() => {
    const categoryId = searchParams.get('categoryId');
    const minSalary = searchParams.get('minSalary');
    const maxSalary = searchParams.get('maxSalary');

    if (categoryId || minSalary || maxSalary) {
      setFilterData((prev) => ({
        ...prev,
        salary: {
          from: minSalary || '',
          to: maxSalary || '',
        },
        // TODO: Można dodać inicjalizację kategorii z URL jeśli potrzebne
      }));
    }
  }, [searchParams]);

  const getSalaryUpdater =
    (type: 'from' | 'to'): Dispatch<SetStateAction<string>> =>
    (value) => {
      const newValue =
        typeof value === 'function' ? value(filterData.salary[type]) : value;

      setFilterData((prev) => ({
        ...prev,
        salary: {
          ...prev.salary,
          [type]: newValue,
        },
      }));
    };

  // Specjalna funkcja obsługi dla LocationSelector
  const handleLocationSelect = (location?: string) => {
    setFilterData((prev) => ({ ...prev, location }));
    closeNestedModal();
  };

  // Specjalna funkcja obsługi dla CategorySelector
  const handleCategorySelect = (selection: CategorySelection) => {
    setFilterData((prev) => ({ ...prev, category: selection }));
    closeNestedModal();
  };

  const handleClear = () => {
    setFilterData(initialFilterData);

    // Usunięcie parametrów filtrowania z URL
    const newParams = new URLSearchParams(searchParams);
    newParams.delete('categoryId');
    newParams.delete('position');
    newParams.delete('minSalary');
    newParams.delete('maxSalary');

    // Invalidacja cache dla ofert
    queryClient.invalidateQueries({
      queryKey: ['get-job-offers'],
    });

    // Aktualizacja URL
    router.push(`/?${newParams.toString()}`);

    // Zamknięcie modala
    closeModal();
  };

  const handleSearch = () => {
    // Przygotowanie nowych parametrów URL
    const newParams = new URLSearchParams(searchParams);

    // Usunięcie starych parametrów filtrowania
    newParams.delete('categoryId');
    newParams.delete('position');
    newParams.delete('minSalary');
    newParams.delete('maxSalary');

    // Dodanie nowych parametrów filtrowania
    if (filterData.category?.category.id) {
      newParams.set('categoryId', filterData.category.category.id.toString());
    }

    if (filterData.salary.from) {
      newParams.set('minSalary', filterData.salary.from);
    }

    if (filterData.salary.to) {
      newParams.set('maxSalary', filterData.salary.to);
    }

    // Invalidacja cache dla ofert
    queryClient.invalidateQueries({
      queryKey: ['get-job-offers'],
    });

    // Aktualizacja URL
    router.push(`/?${newParams.toString()}`);

    // Zamknięcie modala
    closeModal();
  };

  if (isNestedModalOpen && currentNestedModalType === 'city') {
    return (
      <CitySelector
        currentLocation={filterData.location}
        onSelect={handleLocationSelect}
      />
    );
  }

  if (isNestedModalOpen && currentNestedModalType === 'category') {
    return (
      <CategorySelector
        selectedCategory={filterData.category}
        onCategorySelect={handleCategorySelect}
      />
    );
  }

  const location = (() => {
    if (!filterData.location) return '';

    return (
      locations.find(({ id }) => id === filterData.location)?.city.name || ''
    );
  })();

  const categoryDisplayName = (() => {
    if (!filterData.category) return '';

    const { category, subcategory } = filterData.category;
    return subcategory
      ? `${category.name} - ${subcategory.name}`
      : category.name;
  })();

  return (
    <>
      <TextField
        title="Kategoria"
        input={categoryDisplayName}
        setInput={() => {}} // Nie używamy settera, tylko onClick
        placeholder="Wybierz specjalizację"
        borderRadius="rounded"
        onClick={() => openNestedModal('category')}
      />

      <TextField
        title="Lokalizacja"
        input={location}
        setInput={() => {}} // Nie używamy settera, tylko onClick
        placeholder="Wybierz lokalizację"
        borderRadius="rounded"
        onClick={() => openNestedModal('city')}
      />

      <PriceRange
        salaryFrom={filterData.salary.from}
        setSalaryFrom={getSalaryUpdater('from')}
        salaryTo={filterData.salary.to}
        setSalaryTo={getSalaryUpdater('to')}
      />

      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleSearch}
      >
        Wyszukaj
      </Button>

      <Button
        variant="secondary"
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleClear}
      >
        Wyczyść
      </Button>
    </>
  );
}
