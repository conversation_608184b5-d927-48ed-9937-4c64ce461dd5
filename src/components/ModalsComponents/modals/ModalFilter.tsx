import { Dispatch, SetStateAction, useState, useEffect } from 'react';

import { useRouter, useSearchParams } from 'next/navigation';

import { useQueryClient } from '@tanstack/react-query';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';

import { useModal } from '@components/ModalsComponents/context/ModalContext';
import PriceRange from '@components/PriceRange/PriceRange';

import { useCategories } from '@actions/hooks/useCategories';
import { useLocations } from '@actions/hooks/useLocations';

import { CategorySelector, CategorySelection } from './CategorySelector';
import { CitySelector } from './CitySelector';
import { FilterData } from './types';

const initialFilterData: FilterData = {
  category: null,
  location: '',
  salary: {
    from: '',
    to: '',
  },
};

export function ModalFilter() {
  const {
    openNestedModal,
    isNestedModalOpen,
    currentNestedModalType,
    closeNestedModal,
    closeModal,
  } = useModal();
  const { data } = useLocations();
  const locations = data?.data || [];
  const { data: categoriesData } = useCategories();
  const categories = categoriesData?.data || [];
  const router = useRouter();
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  const [filterData, setFilterData] = useState<FilterData>(initialFilterData);
  const [isSearching, setIsSearching] = useState(false);

  // Funkcja do znajdowania kategorii po ID podkategorii lub głównej kategorii
  const findCategoryBySubcategoryId = (subcategoryId: number) => {
    for (const category of categories) {
      const subcategory = category.subcategories?.find(
        (sub) => sub.id === subcategoryId
      );
      if (subcategory) {
        return { category, subcategory };
      }
    }
    return null;
  };

  const findCategoryById = (categoryId: number) => {
    const category = categories.find((cat) => cat.id === categoryId);
    return category ? { category, subcategory: null } : null;
  };

  // Inicjalizacja filtrów z URL przy pierwszym załadowaniu
  useEffect(() => {
    const categoryId = searchParams.get('categoryId');
    const minSalary = searchParams.get('minSalary');
    const maxSalary = searchParams.get('maxSalary');

    if (categoryId || minSalary || maxSalary) {
      let categorySelection: CategorySelection | null = null;

      if (categoryId && categories.length > 0) {
        const categoryIdNum = Number(categoryId);
        // Najpierw sprawdź czy to ID podkategorii
        categorySelection = findCategoryBySubcategoryId(categoryIdNum);
        // Jeśli nie znaleziono, sprawdź czy to ID głównej kategorii
        if (!categorySelection) {
          categorySelection = findCategoryById(categoryIdNum);
        }
      }

      setFilterData((prev) => ({
        ...prev,
        category: categorySelection,
        salary: {
          from: minSalary || '',
          to: maxSalary || '',
        },
      }));
    }
  }, [searchParams, categories]);

  const getSalaryUpdater =
    (type: 'from' | 'to'): Dispatch<SetStateAction<string>> =>
    (value) => {
      const newValue =
        typeof value === 'function' ? value(filterData.salary[type]) : value;

      setFilterData((prev) => ({
        ...prev,
        salary: {
          ...prev.salary,
          [type]: newValue,
        },
      }));
    };

  // Specjalna funkcja obsługi dla LocationSelector
  const handleLocationSelect = (location?: string) => {
    setFilterData((prev) => ({ ...prev, location }));
    closeNestedModal();
  };

  // Specjalna funkcja obsługi dla CategorySelector
  const handleCategorySelect = (selection: CategorySelection) => {
    setFilterData((prev) => ({ ...prev, category: selection }));
    closeNestedModal();
  };

  const handleClear = async () => {
    setIsSearching(true);

    try {
      setFilterData(initialFilterData);

      // Usunięcie parametrów filtrowania z URL
      const newParams = new URLSearchParams(searchParams);
      newParams.delete('categoryId');
      newParams.delete('position');
      newParams.delete('minSalary');
      newParams.delete('maxSalary');
      newParams.delete('activeOffer');

      // Invalidacja cache dla ofert
      await queryClient.invalidateQueries({
        queryKey: ['get-job-offers'],
      });

      // Aktualizacja URL
      router.push(`/?${newParams.toString()}`);

      // Zamknięcie modala
      closeModal();
    } finally {
      setIsSearching(false);
    }
  };

  const handleSearch = async () => {
    setIsSearching(true);

    try {
      // Przygotowanie nowych parametrów URL
      const newParams = new URLSearchParams(searchParams);

      // Usunięcie starych parametrów filtrowania
      newParams.delete('categoryId');
      newParams.delete('position');
      newParams.delete('minSalary');
      newParams.delete('maxSalary');

      // Dodanie nowych parametrów filtrowania (categoryId na końcu)
      if (filterData.salary.from) {
        newParams.set('minSalary', filterData.salary.from);
      }

      if (filterData.salary.to) {
        newParams.set('maxSalary', filterData.salary.to);
      }

      // CategoryId na końcu URL
      if (filterData.category) {
        if (filterData.category.subcategory?.id) {
          // Wybrano konkretną podkategorię - używamy ID podkategorii
          newParams.set(
            'categoryId',
            filterData.category.subcategory.id.toString()
          );
        } else if (filterData.category.category?.id) {
          // Wybrano "Wszystko w kategorii" - używamy ID głównej kategorii
          newParams.set(
            'categoryId',
            filterData.category.category.id.toString()
          );
        }
      }

      // Usunięcie activeOffer z URL, żeby strona automatycznie wybrała pierwszą ofertę
      newParams.delete('activeOffer');

      // Invalidacja cache dla ofert
      await queryClient.invalidateQueries({
        queryKey: ['get-job-offers'],
      });

      // Aktualizacja URL
      router.push(`/?${newParams.toString()}`);

      // Zamknięcie modala
      closeModal();
    } finally {
      setIsSearching(false);
    }
  };

  if (isNestedModalOpen && currentNestedModalType === 'city') {
    return (
      <CitySelector
        currentLocation={filterData.location}
        onSelect={handleLocationSelect}
      />
    );
  }

  if (isNestedModalOpen && currentNestedModalType === 'category') {
    return (
      <CategorySelector
        selectedCategory={filterData.category}
        onCategorySelect={handleCategorySelect}
      />
    );
  }

  const location = (() => {
    if (!filterData.location) return '';

    return (
      locations.find(({ id }) => id === filterData.location)?.city.name || ''
    );
  })();

  const categoryDisplayName = (() => {
    if (!filterData.category) return '';

    const { category, subcategory } = filterData.category;
    return subcategory
      ? `${category.name} - ${subcategory.name}`
      : category.name;
  })();

  return (
    <>
      <TextField
        title="Kategoria"
        input={categoryDisplayName}
        setInput={() => {}} // Nie używamy settera, tylko onClick
        placeholder="Wybierz specjalizację"
        borderRadius="rounded"
        onClick={() => openNestedModal('category')}
      />

      <TextField
        title="Lokalizacja"
        input={location}
        setInput={() => {}} // Nie używamy settera, tylko onClick
        placeholder="Wybierz lokalizację"
        borderRadius="rounded"
        onClick={() => openNestedModal('city')}
      />

      <PriceRange
        salaryFrom={filterData.salary.from}
        setSalaryFrom={getSalaryUpdater('from')}
        salaryTo={filterData.salary.to}
        setSalaryTo={getSalaryUpdater('to')}
      />

      <Button
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleSearch}
        disabled={isSearching}
      >
        {isSearching ? 'Wyszukiwanie...' : 'Wyszukaj'}
      </Button>

      <Button
        variant="secondary"
        borderRadius="rounded"
        fullWidth
        padding="lg"
        onClick={handleClear}
        disabled={isSearching}
      >
        {isSearching ? 'Czyszczenie...' : 'Wyczyść'}
      </Button>
    </>
  );
}
