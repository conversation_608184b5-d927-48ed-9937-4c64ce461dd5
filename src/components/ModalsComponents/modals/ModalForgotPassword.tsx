import { useState } from 'react';

import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { TextField } from '@app/elements/TextFields';

export function ModalForgotPassword() {
  const [email, setEmail] = useState<string>('');
  const t = useTranslations('Modals');

  return (
    <>
      <TextField
        title={t('email')}
        input={email}
        setInput={setEmail}
        placeholder={t('emailPlaceholder')}
        borderRadius="rounded"
      />

      <Button borderRadius="rounded" fullWidth>
        {t('resetPassword')}
      </Button>
    </>
  );
}
