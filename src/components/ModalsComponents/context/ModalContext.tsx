'use client';

import React, { createContext, ReactNode, useContext, useState } from 'react';

import { ModalTypeProps, NestedModalTypeProps } from './types';

// This is used when we have multiple modals (switch between) and we want to have proper name for them instead of using shortcut name (Check ModalLayout.tsx)
// This should match all the strings we have in ModalTypeProps
export const ModalTypeNaming = {
  login: 'Logowanie',
  signin: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  filter: 'Filtruj',
  forgot_password: '<PERSON>apom<PERSON><PERSON><PERSON> hasła',
  SV_edit_offer: 'Edytuj ofertę',
  select_type_of_contract: 'Wybierz rodzaj umowy',
  about_us: 'O Nas',
  company_edit: 'Edytuj dane firmy',
  location_selector: 'Wybierz lokalizację',
  edit_title_salary: 'Edytuj nazwę oraz wynagrodzenie',
  company_size_selector: 'Edytuj rozmiar firmy',
  expectations: 'Oczekiwania',
  requirements: 'Wymagania',
  application: 'Aplikowanie',
  applicationSucces: 'Pomyślen Aplikowanie',
};

// Here we store all modals that we can render
// Keys OF ModalTypeNaming
export const possibleModals = Object.keys(ModalTypeNaming) as Array<
  keyof typeof ModalTypeNaming
>;

export const possibleNestedModals = [
  'localization',
  'city',
  'company_size',
  'about_us',
]; // Here we store all modals that we can render

export type ModalSwitchBetweenProps = Exclude<ModalTypeProps, null>[];

interface ModalContextType {
  // Primary modal
  isModalOpen: boolean;
  openModal: (
    modalType: ModalTypeProps,
    switchBetween?: ModalSwitchBetweenProps, // If we want modal to be able to switch between (for example login and signin)
    backToModal?: ModalTypeProps,
    // onSave will hold any function with any 'data'
    // data can be either string (for single value textfields) or Object for multi variable stuff
    onSave?: null | ((data: any) => void),
    defaultValue?: any
  ) => void;
  closeModal: () => void;

  // Props for singla variable modals
  handleVariableModalSave: null | ((data: any) => void);
  modalDefaultValue: string | null;

  currentModalType: ModalTypeProps;
  switchBetweenModals: ModalSwitchBetweenProps;
  setCurrentModalType: (modalType: ModalTypeProps) => void;
  backToModal: ModalTypeProps;
  setBackToModal: (modalType: ModalTypeProps) => void;

  // Props for nested modal
  isNestedModalOpen: boolean;
  // setCurrentNestedModalType: (modalType: ModalTypeProps) => void;
  openNestedModal: (modalType: NestedModalTypeProps) => void;
  closeNestedModal: () => void;
  currentNestedModalType: NestedModalTypeProps;
}

// Modal context
const ModalContext = createContext<ModalContextType | undefined>(undefined);

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export const ModalProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  // Is modal open (true = open)
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isNestedModalOpen, setIsNestedModalOpen] = useState<boolean>(false);

  // Current type of modal like 'login' or 'signin'
  const [currentModalType, setCurrentModalType] =
    useState<ModalTypeProps>(null);

  const [currentNestedModalType, setCurrentNestedModalType] =
    useState<NestedModalTypeProps>(null);

  // Optional: If we pass array of ModalTypeProps like ['login', 'signin'] we will add buttons to toggle between those modals
  const [switchBetweenModals, setSwitchBetweenModals] =
    useState<ModalSwitchBetweenProps>([]);

  const [backToModal, setBackToModal] = useState<ModalTypeProps>(null); // If we update this to some value from ModalTypeProps like 'login' We will show button 'Go back' or something to go back to previous modal

  // This is very imporant
  // It is used when we open modal from any text field that holds variable
  // we pass function here, that will be triggered ON SAVE (so whenever we save modal)
  // This will hold a function
  // We trigger it after save, to get value BACK to textField
  const [onModalSave, setOnModalSave] = useState<null | ((data: any) => void)>(
    null
  );

  // this is needed for example in 'select_type_of_contract'
  // we need to know what value select as default
  const [modalDefaultValue, setModalDefaultValue] = useState<any>(null);

  // This is function to open primary modal (not nested)
  const openModal = (
    modalType: ModalTypeProps,
    switchModals: ModalSwitchBetweenProps = [],
    backModal: ModalTypeProps = null,
    onSave: null | ((data: any) => void) = null,
    defaultValue: string | null = null
  ) => {
    setIsModalOpen(true);
    setCurrentModalType(modalType);

    // Here we pass function to be triggered after save
    if (onSave) setOnModalSave(() => onSave);

    // Here we default value for currently openned modal
    if (defaultValue) setModalDefaultValue(defaultValue);

    // If we passed switchModals (Array of ModalTypeProps)
    if (switchModals && switchModals.length > 0) {
      setSwitchBetweenModals(switchModals);
    }

    // If we passed modal to go back (nested modals)
    if (backModal && possibleModals.includes(backModal)) {
      setBackToModal(backModal);
    } else {
      // It is needed to clear it every open Modal
      setBackToModal(null);
    }
  };

  const openNestedModal = (modalType: NestedModalTypeProps) => {
    setIsNestedModalOpen(true);
    setCurrentNestedModalType(modalType);
  };

  // Used in modals that are single variable like string or object
  const handleVariableModalSave = (data: any) => {
    if (onModalSave) onModalSave(data);
    closeModal();
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setCurrentModalType(null);
    setBackToModal(null);
    setSwitchBetweenModals([]);
  };

  const closeNestedModal = () => {
    setIsNestedModalOpen(false);
    // setCurrentNestedModalType(null);
  };

  return (
    <ModalContext.Provider
      value={{
        isModalOpen,
        openModal,
        currentModalType,
        closeModal,
        switchBetweenModals,
        setCurrentModalType,
        backToModal,
        setBackToModal,
        // Single variable modals
        handleVariableModalSave,
        modalDefaultValue,
        // Nested modal options
        openNestedModal,
        isNestedModalOpen,
        closeNestedModal,
        currentNestedModalType,
      }}
    >
      {children}
    </ModalContext.Provider>
  );
};
