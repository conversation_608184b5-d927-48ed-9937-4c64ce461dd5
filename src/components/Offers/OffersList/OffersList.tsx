'use client';

import { Fragment, useEffect } from 'react';

import { useInfiniteQuery } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';
import InfiniteScroll from 'react-infinite-scroll-component';

import Loader from '@components/Loader/Loader';
import { SingleOffer } from '@components/Offers/SingleOffer/SingleOffer';

import { getJobOffers } from '@actions/getJobOffers';

import { OfferListProps } from './types';

export function OffersList({
  initialOffersData,
  activeOffer,
  isInitialLoad,
  filters,
}: OfferListProps) {
  const t = useTranslations('OffersList');

  const { data, status, error, hasNextPage, fetchNextPage } = useInfiniteQuery({
    queryKey: ['get-job-offers', filters],
    initialPageParam: 1,
    queryFn: async ({ pageParam }) =>
      getJobOffers({
        currentPage: pageParam,
        ...filters,
      }),
    getNextPageParam: (lastPage, pages) =>
      lastPage?.data && lastPage.data.currentPage < lastPage.data.totalPages
        ? pages.length + 1
        : null,
    initialData: {
      pages: [initialOffersData],
      pageParams: [1],
    },
  });

  useEffect(() => {
    if (!isInitialLoad) return;

    const el = document.getElementById(`offer-${activeOffer}`);

    if (!el) return;

    if (el.getBoundingClientRect().bottom <= window.innerHeight) return;

    el.scrollIntoView({ behavior: 'smooth' });
  }, [isInitialLoad, activeOffer]);

  if (status === 'error') {
    return <div>{error.message}</div>;
  }

  return (
    <div className="w-full" id="scrollableDiv">
      <InfiniteScroll
        dataLength={data.pages.reduce(
          (acc, curr) => acc + (curr?.data?.items.length || 0),
          0
        )}
        next={fetchNextPage}
        hasMore={hasNextPage}
        loader={
          <div className="relative h-80">
            <Loader />
          </div>
        }
        endMessage={
          <p className="text-center text-white">
            <b>{t('thatsAll')}</b>
          </p>
        }
        className="flex flex-1 flex-col gap-4"
        scrollableTarget="scrollableDiv"
      >
        {data.pages.map((page, i) => (
          <Fragment key={i}>
            {page?.data?.items.map((offer, idx) => {
              return (
                <SingleOffer
                  key={`offer-${idx}-${offer.id}`}
                  offer={offer}
                  active={offer.id === activeOffer}
                />
              );
            })}
          </Fragment>
        ))}
      </InfiniteScroll>
    </div>
  );
}
