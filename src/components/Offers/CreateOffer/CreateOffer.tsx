'use client';

import React, { useCallback } from 'react';

import { useRouter } from 'next/navigation';

import { useQueryClient } from '@tanstack/react-query';
import { useTranslations } from 'next-intl';

import { Button } from '@app/elements/Buttons';
import { ImageComponent } from '@app/elements/ImageComponent';
import {
  TextFieldTextAreaClickable,
  TextFieldWithIcon,
} from '@app/elements/TextFields';
import { Text } from '@app/elements/Texts';

import { EditPenIcon } from '@components/icons';
import { useModal } from '@components/ModalsComponents/context/ModalContext';
import { LocationSelector } from '@components/ModalsComponents/modals/LocationSelector';

import { useCreateOffer } from '@actions/hooks/useCreateOffer';
import { useLocations } from '@actions/hooks/useLocations';
import { toast, ToastIconVariant } from '@hooks/use-toast';

import { CreateOfferProps } from './types';
import { useJobOfferEdit } from '../hooks/useJobOfferEdit';

export default function CreateOffer({ companyData }: CreateOfferProps) {
  const t = useTranslations('Offer');
  const { currentNestedModalType, isNestedModalOpen, closeNestedModal } =
    useModal();
  const createOffer = useCreateOffer();
  const router = useRouter();
  const queryClient = useQueryClient();
  const locationsData = useLocations();
  const allLocations = locationsData?.data?.data || [];

  const {
    offerData,
    formatArrayToString,
    handleSetLocation,
    handleLocationClick,
    handleContractTypeClick,
    handleAboutUsClick,
    handleOurExpectationsClick,
    handleOurOfferClick,
    handleTitleSalaryClick,
    handleCompanySizeClick,
  } = useJobOfferEdit();

  const publish = useCallback(async () => {
    const now = new Date();
    const endDate = new Date();
    endDate.setMonth(now.getMonth() + 1);

    const categoryIdToSend = offerData?.category?.subcategory?.id;

    if (!categoryIdToSend) {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        description: 'Musisz wybrać konkretną specjalizację',
      });
      return;
    }

    try {
      await createOffer({
        position: offerData.title,
        category: categoryIdToSend,
        minSalary: offerData.minSalary,
        maxSalary: offerData.maxSalary,
        locations: offerData.locations,
        currency: offerData.currency || 'PLN', // TODO: brakuje UI
        contractType: offerData.contractType?.value,
        startDate: now,
        endDate: endDate,
        aboutCompany: offerData.aboutUs,
        yourDailyTasks: '',
        ourExpectations: offerData.ourExpectations?.[0] || '', // TODO: brakuje array w API
        whatWeOffers: offerData.ourOffer?.[0] || '', // TODO: brakuje array w API
        assignedTo: companyData.company.id,
      });
      // TODO: API powinno zwracać przynajmniej ID nowej oferty - wtedy redirect na tą opertę

      await queryClient.invalidateQueries({
        queryKey: ['getCompany', { id: String(companyData.company.id) }],
      });

      await queryClient.invalidateQueries({
        queryKey: ['get-job-offers'],
      });

      router.push('/');
    } catch (error) {
      console.error('❌ Error creating offer:', error);
    }
  }, [createOffer, offerData, companyData, router, queryClient]);

  const {
    title,
    locations,
    contractType,
    minSalary,
    maxSalary,
    currency,
    companySize, // TODO: nie na ofercie
    aboutUs,
    ourExpectations,
    ourOffer,
    category,
  } = offerData;

  const handleLocationSelect = (selectedLocations: string[]) => {
    handleSetLocation(selectedLocations);
    closeNestedModal();
  };

  if (isNestedModalOpen && currentNestedModalType === 'localization') {
    // TODO: potrzebne?
    return (
      <LocationSelector
        onSelect={handleLocationSelect}
        selectedLocations={locations}
      />
    );
  }

  return (
    <div className="flex size-full flex-col gap-4 overflow-auto rounded-2xl bg-layout-box-bg p-4 backdrop-blur-default laptop:w-3/5">
      <div className="flex items-center gap-3">
        <div className="relative h-[75px] w-[120px] shrink-0 mobile:w-[140px] tablet:w-[160px]">
          <ImageComponent
            src={companyData.company.logo}
            alt="Company Logo"
            fill
            className="rounded-[20px] border-gray-300"
            isLogo
            objectFit="contain"
            priority={false}
          />
        </div>
        <div>
          <div>
            <Text fontSize="md" className="text-white text-opacity-50">
              {companyData.company.name}
            </Text>
          </div>

          <div className="flex items-center gap-3">
            <Text fontSize="lg" className="font-semibold text-white">
              {title || t('addPosition')}
            </Text>

            <EditPenIcon
              onClick={handleTitleSalaryClick}
              size={24}
              className="cursor-pointer"
            />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <TextFieldWithIcon
          title={t('salary')}
          // TODO: salary parser
          input={
            minSalary && maxSalary
              ? `${minSalary} - ${maxSalary} ${currency}`
              : ''
          }
          placeholder={t('addSalary')}
          icon="editPen"
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          onClick={handleTitleSalaryClick}
        />

        <TextFieldWithIcon
          title={t('location')}
          input={
            locations
              ?.map((id) => allLocations?.find((loc) => loc.id === id))
              .map((loc) => {
                if (!loc) return '';
                return `${loc.city.name}${loc.address ? `, ${loc.address}` : ''}`;
              }) || ''
          }
          placeholder={t('addLocation')}
          icon="editPen"
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          isLocationArray
          onClick={handleLocationClick}
        />

        <TextFieldWithIcon
          title={t('companySize')}
          icon="editPen"
          input={companySize}
          placeholder={t('add')}
          textSize="sm"
          iconSize="sm"
          readOnly
          fullWidth
          onClick={handleCompanySizeClick}
        />

        <TextFieldWithIcon
          title={t('contract')}
          input={contractType?.value}
          placeholder={t('addContract')}
          icon="editPen"
          readOnly
          fullWidth
          textSize="sm"
          iconSize="sm"
          onClick={handleContractTypeClick}
        />
      </div>

      <TextFieldTextAreaClickable
        title={t('aboutCompany')}
        value={aboutUs || ''}
        placeholder={t('addAboutCompany')}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        onClick={handleAboutUsClick}
      />

      <TextFieldTextAreaClickable
        title={t('expectations')}
        value={
          ourExpectations && ourExpectations.length > 0
            ? formatArrayToString(ourExpectations)
            : ''
        }
        placeholder={t('addExpectations')}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        shouldResize
        onClick={handleOurExpectationsClick}
      />

      <TextFieldTextAreaClickable
        title={t('whatWeOffer')}
        value={
          ourOffer && ourOffer.length > 0 ? formatArrayToString(ourOffer) : ''
        }
        placeholder={t('addWhatWeOffer')}
        fullWidth
        icon="editPen"
        textSize="sm"
        iconSize="sm"
        shouldResize
        onClick={handleOurOfferClick}
      />

      <Button
        fullWidth
        fontSize="sm"
        onClick={publish}
        disabled={!category?.subcategory}
      >
        {t('publish')}
      </Button>
    </div>
  );
}
