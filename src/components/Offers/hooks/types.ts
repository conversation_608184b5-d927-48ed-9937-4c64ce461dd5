import { CategorySelection } from '@components/ModalsComponents/modals/CategorySelector';

export interface ContractTypeOption {
  key: string;
  value: string;
}

export interface JobOfferData {
  title: string;
  locations: string[];
  contractType: ContractTypeOption;
  minSalary: string;
  maxSalary: string;
  currency: string;
  companySize: string;
  aboutUs: string | null;
  ourExpectations: string[];
  ourOffer: string[];
  category?: CategorySelection | null;
}
