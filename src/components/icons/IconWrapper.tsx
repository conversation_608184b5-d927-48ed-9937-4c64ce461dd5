import React from 'react';

import { iconSizes, IconSizesProps } from '@app/elements/_shared';

import ArrowDownSVG from '@public/assets/icons/arrowDown.svg';
import ArrowDropDownSVG from '@public/assets/icons/arrowDropDown.svg';
import ArrowDropUpSVG from '@public/assets/icons/arrowDropUp.svg';
import ArrowLeftSVG from '@public/assets/icons/arrowLeft.svg';
import CheckSVG from '@public/assets/icons/check.svg';
import CloseSVG from '@public/assets/icons/close.svg';
import DangerSVG from '@public/assets/icons/danger.svg';
import EditPenSVG from '@public/assets/icons/editPen.svg';
import FavoriteSVG from '@public/assets/icons/favorite.svg';
import FilterSVG from '@public/assets/icons/filter.svg';
import InfoCircleSVG from '@public/assets/icons/infoCircle.svg';
import LocationSVG from '@public/assets/icons/location.svg';
import LogoeSVG from '@public/assets/icons/logo.svg';
import MenuSVG from '@public/assets/icons/menu.svg';
import SalarySVG from '@public/assets/icons/salary.svg';
import SearchSVG from '@public/assets/icons/search.svg';
import TickCircleSVG from '@public/assets/icons/tickCircle.svg';
import TrashSVG from '@public/assets/icons/trash.svg';
import UploadSVG from '@public/assets/icons/upload.svg';
import WalletSVG from '@public/assets/icons/wallet.svg';

export interface IconProps extends React.SVGProps<SVGSVGElement> {
  size?: IconSizesProps | number | string;
  onClick?: () => void;
}

/**
 * @param IconComponent Component SVG for wrapping
 * @param displayName Name for component
 */

const createIcon = (
  IconComponent: React.FC<React.SVGProps<SVGSVGElement>>,
  displayName: string
) => {
  const Icon = React.forwardRef<SVGSVGElement, IconProps>(
    (
      {
        size = 'md',
        color = 'white',
        className = '',
        style,
        onClick,
        ...props
      },
      ref
    ) => {
      let sizeClass = '';
      let sizeProps = {};

      if (typeof size === 'string') {
        if (size in iconSizes) {
          sizeClass = iconSizes[size as IconSizesProps];
        } else {
          sizeProps = { width: size, height: size };
        }
      } else if (typeof size === 'number') {
        sizeProps = { width: size, height: size };
      }

      const finalColor = style?.color || color;
      const mergedStyles = { ...style, color: finalColor };

      return (
        <IconComponent
          ref={ref}
          className={`${sizeClass} ${className}`}
          style={mergedStyles}
          onClick={onClick}
          {...sizeProps}
          {...props}
        />
      );
    }
  );

  Icon.displayName = displayName;

  return React.memo(Icon);
};

export const ArrowDownIcon = createIcon(ArrowDownSVG, 'ArrowDownIcon');
export const ArrowLeftIcon = createIcon(ArrowLeftSVG, 'ArrowLeftIcon');
export const ArrowDropDownIcon = createIcon(
  ArrowDropDownSVG,
  'ArrowDropDownIcon'
);
export const ArrowDropUpIcon = createIcon(ArrowDropUpSVG, 'ArrowDropUpIcon');
export const CheckIcon = createIcon(CheckSVG, 'CheckIcon');
export const CloseIcon = createIcon(CloseSVG, 'CloseIcon');
export const DangerIcon = createIcon(DangerSVG, 'DangerIcon');
export const EditPenIcon = createIcon(EditPenSVG, 'EditPenIcon');
export const FavoriteIcon = createIcon(FavoriteSVG, 'FavoriteIcon');
export const FilterIcon = createIcon(FilterSVG, 'FilterIcon');
export const InfoCircleIcon = createIcon(InfoCircleSVG, 'InfoCircleIcon');
export const LocationIcon = createIcon(LocationSVG, 'LocationIcon');
export const LogoIcon = createIcon(LogoeSVG, 'LogoSusnibeIcon');
export const MenuIcon = createIcon(MenuSVG, 'NavbarMenuIcon');
export const SalaryIcon = createIcon(SalarySVG, 'SalaryIcon');
export const SearchIcon = createIcon(SearchSVG, 'SearchIcon');
export const SuccessIcon = createIcon(TickCircleSVG, 'SuccessIcon');
export const TrashIcon = createIcon(TrashSVG, 'TrashIcon');
export const UploadIcon = createIcon(UploadSVG, 'UploadIcon');
export const WalletIcon = createIcon(WalletSVG, 'WalletIcon');

export const Icons = {
  upload: UploadIcon,
  editPen: EditPenIcon,
  trash: TrashIcon,
  close: CloseIcon,
  navbarMenu: MenuIcon,
  arrowDown: ArrowDownIcon,
  arrowLeft: ArrowLeftIcon,
  salary: SalaryIcon,
  search: SearchIcon,
  filter: FilterIcon,
  favorite: FavoriteIcon,
  arrowDropDown: ArrowDropDownIcon,
  arrowDropUp: ArrowDropUpIcon,
  wallet: WalletIcon,
  location: LocationIcon,
  error: DangerIcon,
  success: SuccessIcon,
  info: InfoCircleIcon,
  check: CheckIcon,
  logo: LogoIcon,
};

export type IconName = keyof typeof Icons;
