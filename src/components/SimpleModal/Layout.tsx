'use client';

import React, { useEffect } from 'react';

import { createPortal } from 'react-dom';

import { borderRadiusStyles } from '@app/elements/_shared';
import { ButtonAsIcon } from '@app/elements/Buttons';

import { SimpleModalLayoutProps } from './types';

export default function SimpleModalLayout({
  closeModal,
  children,
}: SimpleModalLayoutProps) {
  useEffect(() => {
    document.body.style.overflow = 'hidden';

    return () => {
      document.body.style.overflow = 'auto';
    };
  }, []);

  // Escape key will close modal
  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (e.key === 'Escape') {
      closeModal();
    }
  };

  return createPortal(
    <div
      className="fixed left-0 top-0 z-20 flex h-screen w-screen animate-fadeIn cursor-default flex-col items-center justify-center gap-4 bg-[#70707050] p-4 backdrop-blur-xl"
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="button"
      aria-label="Close modal"
    >
      <div className="flex w-full max-w-modalMax justify-center gap-4">
        <ButtonAsIcon
          buttonIcon="close"
          iconSize="xl"
          padding="sm"
          onClick={closeModal}
        />
      </div>

      <div
        onKeyDown={handleKeyDown}
        role="none"
        className={`min-h-[100px] w-full max-w-modalMax ${borderRadiusStyles.extra} flex flex-col gap-4 bg-[#ffffff20] p-4 shadow-sm`}
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>,
    document.body
  );
}
