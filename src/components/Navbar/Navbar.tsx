import React from 'react';

import { getLoggedUser } from '@actions/server/getLoggedUser';
import { getLoggedUserCompany } from '@actions/server/getLoggedUserCompany';

import { ClientNavbar } from './ClientNavbar';

async function Navbar() {
  const user = await getLoggedUser();
  const company = user ? await getLoggedUserCompany() : null;

  return <ClientNavbar user={user} company={company?.data} />;
}

export default Navbar;
