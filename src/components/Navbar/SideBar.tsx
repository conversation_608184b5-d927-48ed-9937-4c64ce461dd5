import React, { useCallback, useState } from 'react';

import { useRouter } from 'next/navigation';

import { getCookie, setCookie } from 'cookies-next';
import { useTranslations } from 'next-intl';

// Expanded navbar when we click on burger icon (menu icon)
type SideBarProps = {
  closeNavbar: () => void;
};

export function SideBar({ closeNavbar }: SideBarProps) {
  const [isClosing, setIsClosing] = useState(false);
  const t = useTranslations('Navbar');
  const router = useRouter();

  const handleClose = () => {
    setIsClosing(true);
    setTimeout(() => {
      closeNavbar(); // Close the navbar after the animation finishes
    }, 200); // Match the animation duration
  };

  const locale = getCookie('NEXT_LOCALE') || 'pl';

  const setLanguage = useCallback(
    (newLocale: string) => () => {
      setCookie('NEXT_LOCALE', newLocale);
      router.refresh();
    },
    [router]
  );

  return (
    <div
      role="button"
      aria-label={t('closeNavigation')}
      tabIndex={0}
      className="fixed right-0 top-0 z-50 flex h-screen w-screen cursor-default justify-end overflow-hidden bg-[#11111160]"
      onClick={handleClose}
      onKeyDown={(e) => {
        if (e.key === 'Enter' || e.key === ' ') handleClose();
      }}
    >
      <div
        role="button"
        aria-label={t('closeNavigation')}
        tabIndex={0}
        className={`h-full w-[400px] cursor-auto rounded-l-2xl bg-white p-8 max-tablet:w-full max-tablet:rounded-none ${
          isClosing ? 'animate-slideOut' : 'animate-slideIn'
        }`}
        onClick={(e) => e.stopPropagation()} // Prevent overlay click from closing the navbar
        onKeyDown={() => null}
      >
        <button type="button" className="cursor-pointer" onClick={handleClose}>
          {t('close')}
        </button>

        <div>
          {/* TODO: temporary UI for testing */}
          <button
            type="button"
            className="cursor-pointer"
            onClick={setLanguage('en')}
            disabled={locale === 'en'}
          >
            English
          </button>
          <br />
          <button
            type="button"
            className="cursor-pointer"
            onClick={setLanguage('pl')}
            disabled={locale === 'pl'}
          >
            Polski
          </button>
        </div>
      </div>
    </div>
  );
}
