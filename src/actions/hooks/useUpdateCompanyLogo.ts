import { useTranslations } from 'next-intl';

import { CompanyDetails } from '@components/Company/types';

import { queryWithErrorToast } from '@actions/queryWithErrorToast';
import { toast, ToastIconVariant } from '@hooks/use-toast';

export const useUpdateCompanyLogo = () => {
  const t = useTranslations('Toasts');

  const updateCompanyLogo = async (file: File, id: number) => {
    const form = new FormData();
    form.append('file', file);
    form.append('companyId', id.toString());

    const { data } = await queryWithErrorToast<CompanyDetails>({
      method: 'put',
      url: `/companies/logo`,
      authorized: true,
      data: form,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    if (data) {
      toast({
        iconVariant: ToastIconVariant.SUCCESS,
        title: t('success'),
        description: t('successfullyUpdatedLogo'),
      });
    }

    return {
      data,
    };
  };
  return updateCompanyLogo;
};
