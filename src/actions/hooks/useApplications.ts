import { useQuery } from '@tanstack/react-query';

import { getApplications } from '../getApplications';

export const useApplications = (offerId: number) => {
  // refetch nominalnie do wyrzucenia
  return useQuery({
    queryKey: ['getApplications', offerId],
    queryFn: () => getApplications(offerId),
    refetchInterval: 10 * 60 * 1000, // co 10 minuty gdy aktywna
    refetchIntervalInBackground: false, // STOP gdy karta nieaktywna
    refetchOnWindowFocus: true, // instant reload gdy user wraca na karte
    staleTime: 5 * 60 * 1000, // dane świeze przez 5 minut
  });
};
