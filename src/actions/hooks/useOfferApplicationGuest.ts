import { queryWithErrorToast } from '@actions/queryWithErrorToast';

export const useOfferApplicationGuest = () => {
  const offerApplicationGuest = async (props: {
    jobOffer: number;
    firstName: string;
    lastName: string;
    email: string;
    file: File;
  }) => {
    const form = new FormData();
    form.append('jobOffer', props.jobOffer.toString());
    form.append('firstName', props.firstName);
    form.append('lastName', props.lastName);
    form.append('email', props.email);
    form.append('file', props.file);

    const { data } = await queryWithErrorToast<any>({
      method: 'post',
      url: `/applications/guest`,
      data: form,
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });

    return { data };
  };

  return offerApplicationGuest;
};
