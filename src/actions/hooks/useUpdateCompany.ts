import { useTranslations } from 'next-intl';

import { CompanyDetails } from '@components/Company/types';

import { queryWithErrorToast } from '@actions/queryWithErrorToast';
import { toast, ToastIconVariant } from '@hooks/use-toast';

export const useUpdateCompany = () => {
  const t = useTranslations('Toasts');

  const updateCompany = async (props: any) => {
    const { data } = await queryWithErrorToast<CompanyDetails>({
      method: 'put',
      url: `/companies/profile`,
      authorized: true,
      data: props,
    });

    if (data) {
      toast({
        iconVariant: ToastIconVariant.SUCCESS,
        title: t('success'),
        description: t('successfullyUpdatedCompany'),
      });
    }

    return { data };
  };

  return updateCompany;
};
