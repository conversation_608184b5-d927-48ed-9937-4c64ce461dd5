import { useTranslations } from 'next-intl';

import { queryWithErrorToast } from '@actions/queryWithErrorToast';
import { toast, ToastIconVariant } from '@hooks/use-toast';

export const useCreateOffer = () => {
  const t = useTranslations('Toasts');

  const createOffer = async (props: any) => {
    const { data } = await queryWithErrorToast<any>({
      method: 'post',
      url: `/job-offers`,
      data: props,
      authorized: true,
    });

    if (data) {
      toast({
        iconVariant: ToastIconVariant.SUCCESS,
        title: t('success'),
      });
    }

    return {
      data,
    };
  };

  return createOffer;
};
