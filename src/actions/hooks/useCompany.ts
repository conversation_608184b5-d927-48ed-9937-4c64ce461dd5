import { useQuery } from '@tanstack/react-query';

import { CompanyDetails } from '@components/Company/types';

import { getCompany } from '../getCompany';

export const useCompany = (
  id?: number,
  initialData?: CompanyDetails | null
) => {
  return useQuery({
    queryKey: ['getCompany', { id: String(id) }],
    queryFn: () => getCompany(id || 1),
    enabled: id !== undefined,
    initialData: initialData ? { data: initialData } : undefined,
  });
};
