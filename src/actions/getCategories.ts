import { queryWithErrorToast } from './queryWithErrorToast';

export type Subcategory = {
  id: number;
  name: string;
};

export type Category = {
  id: number;
  name: string;
  subcategories: Subcategory[];
};

export type CategorySelection = {
  category: Category;
  subcategory?: Subcategory;
};

export const getCategories = async () => {
  return queryWithErrorToast<Category[]>({
    method: 'get',
    url: `/categories`,
    authorized: true,
  });
};
