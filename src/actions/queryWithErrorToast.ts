import { toast, ToastIconVariant } from '@hooks/use-toast';

import { query, QueryProps } from './server/query';

const handleError = <Data>({
  data,
  errorMessage,
}: {
  data: Data | null;
  errorMessage?: string;
}) => {
  if (errorMessage) {
    if (typeof window !== 'undefined') {
      toast({
        iconVariant: ToastIconVariant.ERROR,
        description: errorMessage,
      });
    }

    return { data: null };
  }

  return {
    data,
  };
};

export const queryWithErrorToast = async <Response>(props: QueryProps) =>
  handleError(await query<Response>(props));
