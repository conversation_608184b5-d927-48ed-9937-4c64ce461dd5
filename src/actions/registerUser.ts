import { toast, ToastIconVariant } from '@hooks/use-toast';

import { queryWithErrorToast } from './queryWithErrorToast';
import { handleToken } from './server/login';

type Props = {
  email: string;
  password: string;
  confirmPassword: string;
};

export const registerUser = async (props: Props) => {
  const { data } = await queryWithErrorToast<any>({
    method: 'post',
    url: `/users/register`,
    data: props,
  });

  const authorizationToken = data?.response?.authorizationToken;

  if (!authorizationToken) {
    toast({
      iconVariant: ToastIconVariant.ERROR,
      description: 'No authorization token',
    });

    return null;
  }

  return handleToken(authorizationToken);
};
