import { OfferDetailedProps } from '@components/Offers/types';

import { queryWithErrorToast } from './queryWithErrorToast';

export interface Application {
  createdDate: string;
  cv: string;
  id: number;
  jobOffer: OfferDetailedProps;
  updatedDate: string;
  user: {
    uuid: string;
    firstName: string;
    lastName: string;
    email: string;
    avatar?: string;
  };
}

export const getApplications = async (offerId: number) => {
  const { data } = await queryWithErrorToast<Application[]>({
    method: 'get',
    url: '/admin/applications',
    authorized: true,
  });

  const filteredApplications = data?.filter(
    (application) => application.jobOffer.id === offerId
  );

  return {
    data: filteredApplications,
  };
};
