'use server';

import { cookies } from 'next/headers';

import axios, { AxiosResponse } from 'axios';
import { jwtDecode } from 'jwt-decode';

export const handleToken = (authorizationToken: string) => {
  const decoded = jwtDecode<{
    userUuid: string;
    companyId?: number;
    iat: number;
    exp: number;
  }>(authorizationToken);

  if (!decoded) return null;

  const { userUuid, companyId, iat, exp } = decoded;

  const maxAge = exp - iat;

  cookies().set('token', authorizationToken, {
    path: '/',
    maxAge,
    secure: true,
    httpOnly: true,
  });
  cookies().set('userUuid', userUuid, {
    path: '/',
    maxAge,
    secure: true,
  });
  if (companyId) {
    cookies().set('companyId', companyId.toString(), {
      path: '/',
      maxAge,
      secure: true,
    });
  }

  return { userUuid };
};

export const login = async (data: {
  email: string;
  password: string;
}): Promise<{ userUuid?: string; errorMessage?: string } | null> => {
  try {
    const response: AxiosResponse<any> = await axios({
      method: 'post',
      url: `${process.env.NEXT_PUBLIC_API_URL}/users/login`,
      headers: {
        'Content-Type': 'application/json',
        Accept: 'application/json',
      },
      data,
    });

    const authorizationToken = response.data?.response?.authorizationToken;

    if (!authorizationToken)
      return { errorMessage: 'Unexpected error occurred while logging in.' };

    return handleToken(authorizationToken);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return { errorMessage: error.message };
    }

    return { errorMessage: 'Unexpected error occurred while logging in.' };
  }
};
