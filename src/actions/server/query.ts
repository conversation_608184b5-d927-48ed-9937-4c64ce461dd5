'use server';

import { cookies } from 'next/headers';

import axios, { AxiosResponse } from 'axios';

export interface QueryProps {
  data?: any;
  method: 'post' | 'get' | 'put' | 'delete';
  url: string;
  headers?: object;
  authorized?: boolean;
}

export const query = async <Response>({
  data,
  method,
  url,
  headers,
  authorized,
}: QueryProps): Promise<{ data: Response | null; errorMessage?: string }> => {
  try {
    const axiosHeaders: { [key: string]: string } = {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      ...(headers || {}),
    };

    if (authorized) {
      const cookieStore = cookies();
      const cookie = cookieStore.get('token');
      // TODO: check expiration & refresh
      if (cookie) {
        axiosHeaders.Authorization = cookie.value;
      } else {
        // TODO: logout/error
      }
    }

    const response: AxiosResponse<Response> = await axios({
      method,
      url: `${process.env.NEXT_PUBLIC_API_URL}${url}`,
      headers: axiosHeaders,
      data,
    });

    return { data: response.data };
  } catch (error) {
    if (axios.isAxiosError(error)) {
      return { errorMessage: error.message, data: null };
    }

    return { errorMessage: 'Unexpected error occurred.', data: null };
  }
};
