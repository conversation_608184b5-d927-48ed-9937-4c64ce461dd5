'use server';

import { cookies } from 'next/headers';

import { query } from './query';

export interface User {
  uuid: string;
  email: string;
  firstName: string;
  lastName: string;
  headline: string;
  avatar: string | null;
}

export const getLoggedUser = async () => {
  const cookieStore = cookies();
  const userUuid = cookieStore.get('userUuid');

  if (!userUuid) return null;

  const { data } = await query<User>({
    method: 'get',
    url: `/users/${userUuid.value}`,
    authorized: true,
  });

  return data;
};
