'use server';

import { cookies } from 'next/headers';

import { CompanyDetails } from '@components/Company/types';

import { query } from './query';

export const getLoggedUserCompany = async () => {
  const cookieStore = cookies();
  const companyId = cookieStore.get('companyId')?.value;

  if (!companyId) return null;

  return query<CompanyDetails>({
    method: 'get',
    url: `/companies/profile/${companyId}`,
  });
};
