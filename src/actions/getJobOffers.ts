import { OffersResponse } from '@components/Offers/types';

import { queryWithErrorToast } from './queryWithErrorToast';

const OFFERS_PER_PAGE = 10;

export interface JobOffersFilters {
  currentPage: number;
  categoryId?: number;
  categoryIds?: number[]; // Dla filtrowania po wielu kategoriach jednocześnie
  position?: string;
  minSalary?: number;
  maxSalary?: number;
}

// Funkcja pomocnicza do pojedynczego requestu
const getSingleCategoryOffers = async (
  filters: JobOffersFilters,
  categoryId: number
) => {
  const { currentPage, position, minSalary, maxSalary } = filters;

  const params = new URLSearchParams({
    numberOfOffers: OFFERS_PER_PAGE.toString(),
    currentPage: currentPage.toString(),
  });

  params.append('categoryId', categoryId.toString());

  if (position) {
    params.append('position', position);
  }

  if (minSalary) {
    params.append('minSalary', minSalary.toString());
  }

  if (maxSalary) {
    params.append('maxSalary', maxSalary.toString());
  }

  return queryWithErrorToast<OffersResponse>({
    method: 'get',
    url: `/job-offers?${params.toString()}`,
  });
};

export const getJobOffers = async (filters: JobOffersFilters) => {
  const {
    currentPage,
    categoryId,
    categoryIds,
    position,
    minSalary,
    maxSalary,
  } = filters;

  // Jeśli mamy wiele categoryIds, robimy wiele requestów i łączymy wyniki
  if (categoryIds && categoryIds.length > 0) {
    try {
      // Robimy requesty równolegle dla wszystkich kategorii
      const promises = categoryIds.map((catId) =>
        getSingleCategoryOffers(filters, catId)
      );

      const results = await Promise.all(promises);

      // Łączymy wszystkie oferty w jedną tablicę
      const allOffers = results.reduce((acc, result) => {
        if (result?.data?.items) {
          acc.push(...result.data.items);
        }
        return acc;
      }, [] as any[]);

      // Usuwamy duplikaty (jeśli jakaś oferta ma wiele kategorii)
      const uniqueOffers = allOffers.filter(
        (offer, index, self) =>
          index === self.findIndex((o) => o.id === offer.id)
      );

      // Sortujemy po dacie utworzenia (najnowsze pierwsze)
      uniqueOffers.sort(
        (a, b) =>
          new Date(b.createdDate).getTime() - new Date(a.createdDate).getTime()
      );

      // Zwracamy w formacie zgodnym z OffersResponse
      return {
        data: {
          items: uniqueOffers,
          totalCount: uniqueOffers.length,
          hasNextPage: false, // Dla uproszczenia - można by to lepiej obsłużyć
        },
      };
    } catch (error) {
      // Jeśli wystąpi błąd, zwracamy pustą odpowiedź
      return {
        data: {
          items: [],
          totalCount: 0,
          hasNextPage: false,
        },
      };
    }
  }

  // Standardowy request dla pojedynczej kategorii
  const params = new URLSearchParams({
    numberOfOffers: OFFERS_PER_PAGE.toString(),
    currentPage: currentPage.toString(),
  });

  if (categoryId) {
    params.append('categoryId', categoryId.toString());
  }

  if (position) {
    params.append('position', position);
  }

  if (minSalary) {
    params.append('minSalary', minSalary.toString());
  }

  if (maxSalary) {
    params.append('maxSalary', maxSalary.toString());
  }

  return queryWithErrorToast<OffersResponse>({
    method: 'get',
    url: `/job-offers?${params.toString()}`,
  });
};
