import { OffersResponse } from '@components/Offers/types';

import { queryWithErrorToast } from './queryWithErrorToast';

const OFFERS_PER_PAGE = 10;

export interface JobOffersFilters {
  currentPage: number;
  categoryId?: number;
  position?: string;
  minSalary?: number;
  maxSalary?: number;
}

export const getJobOffers = async (filters: JobOffersFilters) => {
  const { currentPage, categoryId, position, minSalary, maxSalary } = filters;

  const params = new URLSearchParams({
    numberOfOffers: OFFERS_PER_PAGE.toString(),
    currentPage: currentPage.toString(),
  });

  if (categoryId) {
    params.append('categoryId', categoryId.toString());
  }

  if (position) {
    params.append('position', position);
  }

  if (minSalary) {
    params.append('minSalary', minSalary.toString());
  }

  if (maxSalary) {
    params.append('maxSalary', maxSalary.toString());
  }

  const finalUrl = `/job-offers?${params.toString()}`;
  console.log('🌐 API Request URL:', finalUrl);
  console.log('📊 Filters:', filters);

  return queryWithErrorToast<OffersResponse>({
    method: 'get',
    url: finalUrl,
  });
};
