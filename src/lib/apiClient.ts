import axios, { AxiosResponse, AxiosRequestConfig } from 'axios';

type ApiResponse<T> = {
  data: T;
  status: number;
  headers: any;
};

/**
 * Generic API client utility for making HTTP requests
 */
export const apiClient = {
  /**
   * Make a GET request
   * @param endpoint - API endpoint path
   * @param config - Optional axios config
   * @returns Promise with response data, status and headers
   */
  get: async <T>(
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axios.get(
        `${process.env.NEXT_PUBLIC_API_URL}${endpoint}`,
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            ...(config?.headers || {}),
          },
          ...config,
        }
      );

      return {
        data: response.data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          `API Error: ${error.response?.status || 'Unknown Status'} - ${error.message}`
        );
      }
      throw new Error('Unexpected error occurred while making GET request.');
    }
  },

  /**
   * Make a POST request
   * @param endpoint - API endpoint path
   * @param data - Request payload
   * @param config - Optional axios config
   * @returns Promise with response data, status and headers
   */
  post: async <T>(
    endpoint: string,
    data: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL}${endpoint}`,
        data,
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            ...(config?.headers || {}),
          },
          ...config,
        }
      );

      return {
        data: response.data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          `API Error: ${error.response?.status || 'Unknown Status'} - ${error.message}`
        );
      }
      throw new Error('Unexpected error occurred while making POST request.');
    }
  },

  /**
   * Make a PUT request
   * @param endpoint - API endpoint path
   * @param data - Request payload
   * @param config - Optional axios config
   * @returns Promise with response data, status and headers
   */
  put: async <T>(
    endpoint: string,
    data: any,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axios.put(
        `${process.env.NEXT_PUBLIC_API_URL}${endpoint}`,
        data,
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            ...(config?.headers || {}),
          },
          ...config,
        }
      );

      return {
        data: response.data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          `API Error: ${error.response?.status || 'Unknown Status'} - ${error.message}`
        );
      }
      throw new Error('Unexpected error occurred while making PUT request.');
    }
  },

  /**
   * Make a PUT request with FormData (for file uploads)
   * @param endpoint - API endpoint path
   * @param formData - FormData instance
   * @param config - Optional axios config
   * @returns Promise with response data, status and headers
   */
  putForm: async <T>(
    endpoint: string,
    formData: FormData,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axios.putForm(
        `${process.env.NEXT_PUBLIC_API_URL}${endpoint}`,
        formData,
        {
          headers: {
            ...(config?.headers || {}),
          },
          ...config,
        }
      );

      return {
        data: response.data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          `API Error: ${error.response?.status || 'Unknown Status'} - ${error.message}`
        );
      }
      throw new Error(
        'Unexpected error occurred while making PUT form request.'
      );
    }
  },

  /**
   * Make a DELETE request
   * @param endpoint - API endpoint path
   * @param config - Optional axios config
   * @returns Promise with response data, status and headers
   */
  delete: async <T>(
    endpoint: string,
    config?: AxiosRequestConfig
  ): Promise<ApiResponse<T>> => {
    try {
      const response: AxiosResponse<T> = await axios.delete(
        `${process.env.NEXT_PUBLIC_API_URL}${endpoint}`,
        {
          headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            ...(config?.headers || {}),
          },
          ...config,
        }
      );

      return {
        data: response.data,
        status: response.status,
        headers: response.headers,
      };
    } catch (error) {
      if (axios.isAxiosError(error)) {
        throw new Error(
          `API Error: ${error.response?.status || 'Unknown Status'} - ${error.message}`
        );
      }
      throw new Error('Unexpected error occurred while making DELETE request.');
    }
  },
};
