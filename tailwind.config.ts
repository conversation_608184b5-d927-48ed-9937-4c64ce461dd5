/* eslint-disable global-require */
import type { Config } from 'tailwindcss';

const config: Config = {
  darkMode: ['class'],
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    screens: {
      mobile: '400px',
      // => @media (min-width: 400px) { ... }

      tablet: '768px',
      // => @media (min-width: 768px) { ... }

      laptop: '1024px',
      // => @media (min-width: 1024px) { ... }

      desktop: '1280px',
      // => @media (min-width: 1280px) { ... }

      full: '1440px',
      // => @media (min-width: 1440px) { ... }
    },
    extend: {
      colors: {
        /* Global  */
        'layout-bg': '#111',
        'layout-box-bg': '#FFFFFF30',
        'layout-box-bg-hover': '#FFFFFF40',
        'layout-box-bg-active': '#FFFFFF70',

        // toast
        'toast-bg': '#FFFFFF20',
        'toast-border': '#FFFFFF40',

        // text-fileds.tsx
        'tf-border': 'var(--tf-border)',
        'tf-border-focus': 'var(--tf-border-focus)',
        'tf-text-title': 'var(--tf-text-title)',
        'tf-text-content': 'var(--tf-text-content)',

        // radio.tsx
        'radio-border': 'var(--radio-border)',
        'radio-checked-border': 'var(--radio-checked-border)',
        'radio-hover-bg': 'var(--radio-hover-bg)',
        'radio-primary': 'var(--radio-primary)',
        'radio-additional': 'var(--radio-additional)',

        // buttons.tsx
        // Primary
        'btn-primary-bg': 'var(--btn-primary-bg)',
        'btn-primary-color': 'var(--btn-primary-color)',
        'btn-primary-hover-bg': 'var(--btn-primary-hover-bg)',

        // Secondary
        'btn-secondary-bg': 'var(--btn-secondary-bg)',
        'btn-secondary-border': 'var(--btn-secondary-border)',
        'btn-secondary-color': 'var(--btn-secondary-color)',
        'btn-secondary-hover-bg': 'var(--btn-secondary-hover-bg)',

        background: 'hsl(var(--background))',
        foreground: 'hsl(var(--foreground))',
        card: {
          DEFAULT: 'hsl(var(--card))',
          foreground: 'hsl(var(--card-foreground))',
        },
        popover: {
          DEFAULT: 'hsl(var(--popover))',
          foreground: 'hsl(var(--popover-foreground))',
        },
        primary: {
          DEFAULT: 'hsl(var(--primary))',
          foreground: 'hsl(var(--primary-foreground))',
        },
        secondary: {
          DEFAULT: 'hsl(var(--secondary))',
          foreground: 'hsl(var(--secondary-foreground))',
        },
        muted: {
          DEFAULT: 'hsl(var(--muted))',
          foreground: 'hsl(var(--muted-foreground))',
        },
        accent: {
          DEFAULT: 'hsl(var(--accent))',
          foreground: 'hsl(var(--accent-foreground))',
        },
        destructive: {
          DEFAULT: 'hsl(var(--destructive))',
          foreground: 'hsl(var(--destructive-foreground))',
        },
        border: 'hsl(var(--border))',
        input: 'hsl(var(--input))',
        ring: 'hsl(var(--ring))',
        chart: {
          '1': 'hsl(var(--chart-1))',
          '2': 'hsl(var(--chart-2))',
          '3': 'hsl(var(--chart-3))',
          '4': 'hsl(var(--chart-4))',
          '5': 'hsl(var(--chart-5))',
        },
      },
      maxWidth: {
        // text-fields.tsx
        tf: '500px',

        // modals.tsx / modal-layout.tsx
        modalMax: '500px',

        // radio.tsx
        radio: '500px',
      },
      transitionDuration: {
        default: '200ms',
      },
      scale: {
        default: '1.1',
      },
      backgroundImage: {
        // macOs bg from figa
        // 'macos-bg': "url('/assets/images/macos-bg.jpg')",
        'medical-bg': "url('/assets/images/medical-bg.svg')",
      },
      borderRadius: {
        lg: 'var(--radius)',
        md: 'calc(var(--radius) - 2px)',
        sm: 'calc(var(--radius) - 4px)',
      },
      backdropBlur: {
        default: '40px',
      },
      keyframes: {
        'accordion-down': {
          from: {
            height: '0',
          },
          to: {
            height: 'var(--radix-accordion-content-height)',
          },
        },
        'accordion-up': {
          from: {
            height: 'var(--radix-accordion-content-height)',
          },
          to: {
            height: '0',
          },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideIn: {
          '0%': { transform: 'translateX(100%)' },
          '100%': { transform: 'translateX(0)' },
        },
        slideOut: {
          '0%': { transform: 'translateX(0)' },
          '100%': { transform: 'translateX(100%)' },
        },
        pulseShadow: {
          '0%, 100%': { boxShadow: '0 0 0 0 rgba(255, 0, 0, 0.4)' },
          '50%': { boxShadow: '0 0 15px 10px rgba(255, 0, 0, 0.2)' },
        },
      },

      animation: {
        'accordion-down': 'accordion-down 0.2s ease-out',
        'accordion-up': 'accordion-up 0.2s ease-out',
        slideIn: 'slideIn 0.2s ease-out forwards',
        slideOut: 'slideOut 0.2s ease-out forwards',
        fadeIn: 'fadeIn 0.2s ease-in-out',
        pulseShadow: 'pulseShadow 0.3s ease-in-out forwards',
      },
    },
  },
  plugins: [require('tailwindcss-animate')],
};
export default config;
