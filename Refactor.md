1. <PERSON><PERSON><PERSON><PERSON> margines na liście ofert (kazdy ekran) (b<PERSON><PERSON><PERSON> w stylach).

2. Our expectations i daily tasks powinno być string[].

3. Brakuje company size w endpoincie companies/profile/id

4. Odnośnie strony głównej z listą ofert - przy zmianie rozmiaru przeglądarki widok się nie zmienia na taki jaki powinien

5. <PERSON>e ma jak ed<PERSON> oferty - dorobić endpoint do edycji oferty

6. Brakuje aboutCompany w put na companies/profile

7. Podbicie react do wersji 19? nie musielibysmy się martwić o uzycia useMemo, useCallback itd

8. parametr 'status': "new" do enpointu z aplikacją uzytkownika? aby pokazywać indicator "Nowa" dopóki rekruter nie otworzy aplikacji?

9. Skąd brać numer uzytkownika na liście aplikacji i czy wgl chcemy pokazywać nr telefonu?

10. https backend i front

11. company size i about company w company a nie w offer

12. Tworzenie oferty UI - brakuje category, currency, location (pobieranie z BE, dodawanie nowej lokalizacji, wysyłanie id przy tworzeniu), yourDailyTasks,

13. Tworzenie oferty BE - brakuje pobrania ID mojej company, post na offer powinien zwracać id utworzonej oferty (wtedy my na nią przekierujemy)

14. Pobieranie aplikacji BE - brakuje filtracji - jak pobrać aplikacje do jednej oferty? Brakuje danych usera - przy aplikowaniu podałem first i last name ale przy pobieraniu dostaję puste stringi, get na api/admin/applications zwraca, get na api/applications nic nie zwraca

15. storybook

16. Refresh sesji/wylogowywanie jak sesja wygaśnie

17. Refresh token/relog po stworzeniu firmy - brakuje company w aktualnym tokenie

18. na teraz pobieranie wszystkich apliakcji (cvek) po stronie frontu uzywając libki jszip - nominalnie endpoint do pobierania all cvek gdyz:
    Oferta na Senior Developer = 80 aplikacji
    Frontend: 80 requestów + tworzenie ZIP w przeglądarce
    Backend: 1 request + gotowy ZIP
