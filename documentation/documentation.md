
## Main file that describes all logic and rules for this app

Please follow this documentation, keep it clean and updated.

_App created at: 30.09.2024_

### Swagger Endpoint

http://207.154.200.219:3002/api/swagger

Database choice is PostgreSQL

### File Structure

---

- src
  - locales | _All languages that we support for translation in .json format_
 - notes | _Here, we put our file (one per dev) called like Name_Lastname.md and we want to write our logs there and all updates_
  - app
    - [locale]
      - page.tsx | _Starting point for out app_
      - layout.tsx | _Main file where we have HTML and BODY element and here we add Context like Auth or Theme Provider_

  - utils | _Files that contain multipurpose functions and some helpers_   
    - logger.ts | _Our custom logger to use across the app_ 
    
  - config 
    - globals.ts | _Here we put variables that can be used more than once or need to be easy to moderate in one place_
  - components | _All componenets that consists of one or more elements_
  - elements | _All our custom elements that consists mostly from default html tag like button or input_
    - buttons
      - radio.tsx | _Radio buttons to use across the app_ 
      - text-fields.tsx | _All text fields_ 

### Important Settings in VS Code 

--- 

(Visual Code) <br/>
We want to format our code on save with: <br/>
TypeScript and JavaScript Language features <br/>
or <br/>
Prettier (I'm not a big fan of this)


### App Language (translation)
---
We are using NextJs i18n routing \
We automatically get users location and add it to path like 
- localhost:8000/en/...
- localhost:8000/[locales]/...

Video for this setup: \
https://www.youtube.com/watch?v=uZQ5d2bRMO4

Link to NextJs documentation:\
https://next-intl-docs.vercel.app/docs/getting-started/app-router/with-i18n-routing


## Function 
We write functions as Arrow Functions 
```ts
const myArrowFunction = () => {
  console.log("This is an arrow function expression!");
};
```

## Colors 
We use **HEX** as default color format, and please keep that everywhere

## Animations 
Every animation and transition should be at 200 ms

## Icons 
We put svg icons in **/public/assets/icons**


### Console logging

---

Use our custom function in /utils/logger.ts

```ts
logInfo("ThemeProvider", "Some message");
logWarn("setObjectInLocalStorage", "Some message");
```

Where first argument is a callFrom meaning what function or class calls this log \
Second is our actuall message

### Code description / commenting

--- 

We want to add description at the very top of file to explain what it does <br/>
We want to add comments to every function and place that needs some explanation
```js 
// Description:
// This is our logger function that we can use instead of console.log

// How to use this: 
// logInfo('This is an info message.');
// logWarn('This is a warning message.');
// logError('This is an error message.');
// logDebug('This is a debug message with a variable:', 42);
```

### Naming conventions

---

**Files and Folders**\
We use kebab-case for that \
example:

- about-us.jsx
- header.jsx

**Reausable Componenets**\
We use PascalCase \
example:

- Header.jsx
- SomeComponent.jsx

**Functions**\
We use camelCase\
example:

- fetchData()
- handleButtonClick()

**Variables**\
We use camelCase if possible
example:

- callFrom
- latestIndex

For global variables we use UPPER_SNAKE_CASE
example:

- API_URL
- MAX_RETRIES_IDX

**TypeScript Types and other**\
We use PascalCase\
example:

- Metadata
- ThemeContextType

**Styles**\
We write styles using Tailwind

